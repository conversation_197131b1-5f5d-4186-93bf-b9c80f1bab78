import { resolve } from 'node:path'
import { fileURLToPath, URL } from 'node:url'

import uni from '@dcloudio/vite-plugin-uni'
import Components from '@uni-helper/vite-plugin-uni-components'
import { WotResolver } from '@uni-helper/vite-plugin-uni-components/resolvers'
import { defineConfig } from 'vite'

export default defineConfig({
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  plugins: [
    Components({
      dts: true,
      resolvers: [WotResolver()],
    }),
    uni(),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @import "@/styles/global.scss";
          @import "@/styles/global-fonts.scss";
          @import "@/styles/flex.scss";
        `,
      },
    },
  },
})
