{
  "pages": [
    {
      "path": "pages/index",
      "type": "home"
    }
  ],
  "globalStyle": {
    "backgroundColor": "#ffffff",
    "backgroundColorBottom": "@bgColorBottom",
    "backgroundColorTop": "#ffffff",
    "backgroundTextStyle": "@bgTxtStyle",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "",
    "navigationStyle": "custom",
    "safeAreaInsets": {
      "top": "env(safe-area-inset-top)"
    },
    "usingComponents": {
      "uni-card": "/uni_modules/uni-card/components/uni-card/uni-card"
    }
  },
  "tabBar": {
    "custom": true, // 启用自定义TabBar
    "list": [
      { "pagePath": "subPackages/baby/index" },
      { "pagePath": "subPackages/accompany/index" },
      // { "pagePath": "subPackages/publish/index", "isSpecial": true }, // 特殊按钮标识
      { "pagePath": "subPackages/store/index" },
      { "pagePath": "subPackages/mine/index" }
    ]
  },
  "subPackages": [
    {
      "root": "subPackages/baby",
      "pages": [
        {
          "path": "index",
          // "style": { "navigationStyle": "custom", "app-plus": { "softinputMode": "adjustResize" } },
          "style": {
            // "navigationBarBackgroundColor": "rgba(0,0,0,0)",
            "app-plus": {
              "softinputMode": "adjustPan",
              "titleNView": {
                "type": "float" // 悬浮导航栏模式
              }
            }
          }
        },
        { "path": "playVideo", "style": { "navigationStyle": "custom" } },
        { "path": "babyInfo/index", "style": { "navigationStyle": "custom" } },
        { "path": "infoSet/index", "style": { "navigationStyle": "custom" } },
        { "path": "setHeight/index", "style": { "navigationStyle": "custom" } },
        { "path": "memberManage/index", "style": { "navigationStyle": "custom" } },
        { "path": "newPost/index", "style": { "navigationStyle": "custom" } }
      ]
    },
    {
      "root": "subPackages/accompany",
      "pages": [
        { "path": "index" },
        { "path": "dialogue" },
        { "path": "dialogueList" },
        {
          "path": "evaluation",
          "style": {
            "disableSwipeBack": true
          }
        },
        {
          "path": "reportList",
          "style": {
            // "navigationStyle": "default",
            // "navigationBarTextStyle": "black",
            // "navigationBarBackgroundColor": "#ffffff",
            // "navigationBarTitleText": "Report"
          }
        },
        { "path": "reportDetail" },
        { "path": "bedtimeStory" }
      ]
    },
    {
      "root": "subPackages/publish", // 发布中心（橙色加号）
      "pages": [{ "path": "index" }]
    },
    {
      "root": "subPackages/store",
      "pages": [
        {
          "path": "index",
          "style": {
            "app-plus": {
              "titleNView": false // 隐藏导航栏
            }
          }
        }
      ]
    },
    {
      "root": "subPackages/mine",
      "pages": [
        { "path": "index" },
        { "path": "personInfor" },
        { "path": "profilePicture" },
        { "path": "settings" },
        { "path": "accountSecurity" },
        { "path": "mobilePhone" },
        { "path": "passwordModification" },
        { "path": "myOrder" },
        { "path": "privacy" },
        { "path": "privacyDetail" }
      ]
    },
    {
      "root": "subPackages/h5",
      "pages": [
        { "path": "invitation" },
        { "path": "manual" }
      ]
    },
    {
      "root": "subPackages/login",
      "pages": [
        {
          "path": "index",
          "style": {
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "forgotPassword",
          "style": {
            "app-plus": {
              "titleNView": false
            }
          }
        },
        {
          "path": "resetSuccess",
          "style": {
            "app-plus": {
              "titleNView": false
            }
          }
        }
      ]
    }
  ],
  "preloadRule": {
    "pages/index/index": {
      "packages": ["subPackages/store"]
    }
  },
	"condition" : { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "", //模式名称
				"path": "/subPackages/accompany/bedtimeStory", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到
			}
		]
	}
}
