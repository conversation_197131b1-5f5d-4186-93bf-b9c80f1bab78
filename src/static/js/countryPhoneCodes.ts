const countryPhoneCodes = [{
  country: 'Afghanistan',
  value: '+93',
  iso: 'AF',
}, {
  country: 'Albania',
  value: '+355',
  iso: 'AL',
}, {
  country: 'Algeria',
  value: '+213',
  iso: 'DZ',
}, {
  country: 'American Samoa',
  value: '******',
  iso: 'AS',
}, {
  country: 'Andorra',
  value: '+376',
  iso: 'AD',
}, {
  country: 'Angola',
  value: '+244',
  iso: 'AO',
}, {
  country: 'Anguilla',
  value: '******',
  iso: 'AI',
}, {
  country: 'Antarctica',
  value: '+672',
  iso: 'AQ',
}, {
  country: 'Antigua and Barbuda',
  value: '******',
  iso: 'AG',
}, {
  country: 'Argentina',
  value: '+54',
  iso: 'AR',
}, {
  country: 'Armenia',
  value: '+374',
  iso: 'AM',
}, {
  country: 'Aruba',
  value: '+297',
  iso: 'AW',
}, {
  country: 'Australia',
  value: '+61',
  iso: 'AU',
}, {
  country: 'Austria',
  value: '+43',
  iso: 'AT',
}, {
  country: 'Azerbaijan',
  value: '+994',
  iso: 'AZ',
}, {
  country: 'Bahamas',
  value: '******',
  iso: 'BS',
}, {
  country: 'Bahrain',
  value: '+973',
  iso: 'BH',
}, {
  country: 'Bangladesh',
  value: '+880',
  iso: 'BD',
}, {
  country: 'Barbados',
  value: '+1-246',
  iso: 'BB',
}, {
  country: 'Belarus',
  value: '+375',
  iso: 'BY',
}, {
  country: 'Belgium',
  value: '+32',
  iso: 'BE',
}, {
  country: 'Belize',
  value: '+501',
  iso: 'BZ',
}, {
  country: 'Benin',
  value: '+229',
  iso: 'BJ',
}, {
  country: 'Bermuda',
  value: '+1-441',
  iso: 'BM',
}, {
  country: 'Bhutan',
  value: '+975',
  iso: 'BT',
}, {
  country: 'Bolivia',
  value: '+591',
  iso: 'BO',
}, {
  country: 'Bosnia and Herzegovina',
  value: '+387',
  iso: 'BA',
}, {
  country: 'Botswana',
  value: '+267',
  iso: 'BW',
}, {
  country: 'Brazil',
  value: '+55',
  iso: 'BR',
}, {
  country: 'British Indian Ocean Territory',
  value: '+246',
  iso: 'IO',
}, {
  country: 'British Virgin Islands',
  value: '+1-284',
  iso: 'VG',
}, {
  country: 'Brunei',
  value: '+673',
  iso: 'BN',
}, {
  country: 'Bulgaria',
  value: '+359',
  iso: 'BG',
}, {
  country: 'Burkina Faso',
  value: '+226',
  iso: 'BF',
}, {
  country: 'Burundi',
  value: '+257',
  iso: 'BI',
}, {
  country: 'Cambodia',
  value: '+855',
  iso: 'KH',
}, {
  country: 'Cameroon',
  value: '+237',
  iso: 'CM',
}, {
  country: 'Canada',
  value: '+1',
  iso: 'CA',
}, {
  country: 'Cape Verde',
  value: '+238',
  iso: 'CV',
}, {
  country: 'Cayman Islands',
  value: '+1-345',
  iso: 'KY',
}, {
  country: 'Central African Republic',
  value: '+236',
  iso: 'CF',
}, {
  country: 'Chad',
  value: '+235',
  iso: 'TD',
}, {
  country: 'Chile',
  value: '+56',
  iso: 'CL',
}, {
  country: 'China',
  value: '+86',
  iso: 'CN',
}, {
  country: 'Christmas Island',
  value: '+61',
  iso: 'CX',
}, {
  country: 'Cocos Islands',
  value: '+61',
  iso: 'CC',
}, {
  country: 'Colombia',
  value: '+57',
  iso: 'CO',
}, {
  country: 'Comoros',
  value: '+269',
  iso: 'KM',
}, {
  country: 'Cook Islands',
  value: '+682',
  iso: 'CK',
}, {
  country: 'Costa Rica',
  value: '+506',
  iso: 'CR',
}, {
  country: 'Croatia',
  value: '+385',
  iso: 'HR',
}, {
  country: 'Cuba',
  value: '+53',
  iso: 'CU',
}, {
  country: 'Curacao',
  value: '+599',
  iso: 'CW',
}, {
  country: 'Cyprus',
  value: '+357',
  iso: 'CY',
}, {
  country: 'Czech Republic',
  value: '+420',
  iso: 'CZ',
}, {
  country: 'Democratic Republic of the Congo',
  value: '+243',
  iso: 'CD',
}, {
  country: 'Denmark',
  value: '+45',
  iso: 'DK',
}, {
  country: 'Djibouti',
  value: '+253',
  iso: 'DJ',
}, {
  country: 'Dominica',
  value: '+1-767',
  iso: 'DM',
}, {
  country: 'Dominican Republic',
  value: '+1-809',
  iso: 'DO',
}, {
  country: 'Dominican Republic',
  value: '+1-829',
  iso: 'DO',
}, {
  country: 'Dominican Republic',
  value: '+1-849',
  iso: 'DO',
}, {
  country: 'East Timor',
  value: '+670',
  iso: 'TL',
}, {
  country: 'Ecuador',
  value: '+593',
  iso: 'EC',
}, {
  country: 'Egypt',
  value: '+20',
  iso: 'EG',
}, {
  country: 'El Salvador',
  value: '+503',
  iso: 'SV',
}, {
  country: 'Equatorial Guinea',
  value: '+240',
  iso: 'GQ',
}, {
  country: 'Eritrea',
  value: '+291',
  iso: 'ER',
}, {
  country: 'Estonia',
  value: '+372',
  iso: 'EE',
}, {
  country: 'Ethiopia',
  value: '+251',
  iso: 'ET',
}, {
  country: 'Falkland Islands',
  value: '+500',
  iso: 'FK',
}, {
  country: 'Faroe Islands',
  value: '+298',
  iso: 'FO',
}, {
  country: 'Fiji',
  value: '+679',
  iso: 'FJ',
}, {
  country: 'Finland',
  value: '+358',
  iso: 'FI',
}, {
  country: 'France',
  value: '+33',
  iso: 'FR',
}, {
  country: 'French Polynesia',
  value: '+689',
  iso: 'PF',
}, {
  country: 'Gabon',
  value: '+241',
  iso: 'GA',
}, {
  country: 'Gambia',
  value: '+220',
  iso: 'GM',
}, {
  country: 'Georgia',
  value: '+995',
  iso: 'GE',
}, {
  country: 'Germany',
  value: '+49',
  iso: 'DE',
}, {
  country: 'Ghana',
  value: '+233',
  iso: 'GH',
}, {
  country: 'Gibraltar',
  value: '+350',
  iso: 'GI',
}, {
  country: 'Greece',
  value: '+30',
  iso: 'GR',
}, {
  country: 'Greenland',
  value: '+299',
  iso: 'GL',
}, {
  country: 'Grenada',
  value: '+1-473',
  iso: 'GD',
}, {
  country: 'Guam',
  value: '+1-671',
  iso: 'GU',
}, {
  country: 'Guatemala',
  value: '+502',
  iso: 'GT',
}, {
  country: 'Guernsey',
  value: '+44-1481',
  iso: 'GG',
}, {
  country: 'Guinea',
  value: '+224',
  iso: 'GN',
}, {
  country: 'Guinea-Bissau',
  value: '+245',
  iso: 'GW',
}, {
  country: 'Guyana',
  value: '+592',
  iso: 'GY',
}, {
  country: 'Haiti',
  value: '+509',
  iso: 'HT',
}, {
  country: 'Honduras',
  value: '+504',
  iso: 'HN',
}, {
  country: 'Hong Kong',
  value: '+852',
  iso: 'HK',
}, {
  country: 'Hungary',
  value: '+36',
  iso: 'HU',
}, {
  country: 'Iceland',
  value: '+354',
  iso: 'IS',
}, {
  country: 'India',
  value: '+91',
  iso: 'IN',
}, {
  country: 'Indonesia',
  value: '+62',
  iso: 'ID',
}, {
  country: 'Iran',
  value: '+98',
  iso: 'IR',
}, {
  country: 'Iraq',
  value: '+964',
  iso: 'IQ',
}, {
  country: 'Ireland',
  value: '+353',
  iso: 'IE',
}, {
  country: 'Isle of Man',
  value: '+44-1624',
  iso: 'IM',
}, {
  country: 'Israel',
  value: '+972',
  iso: 'IL',
}, {
  country: 'Italy',
  value: '+39',
  iso: 'IT',
}, {
  country: 'Ivory Coast',
  value: '+225',
  iso: 'CI',
}, {
  country: 'Jamaica',
  value: '+1-876',
  iso: 'JM',
}, {
  country: 'Japan',
  value: '+81',
  iso: 'JP',
}, {
  country: 'Jersey',
  value: '+44-1534',
  iso: 'JE',
}, {
  country: 'Jordan',
  value: '+962',
  iso: 'JO',
}, {
  country: 'Kazakhstan',
  value: '+7',
  iso: 'KZ',
}, {
  country: 'Kenya',
  value: '+254',
  iso: 'KE',
}, {
  country: 'Kiribati',
  value: '+686',
  iso: 'KI',
}, {
  country: 'Kosovo',
  value: '+383',
  iso: 'XK',
}, {
  country: 'Kuwait',
  value: '+965',
  iso: 'KW',
}, {
  country: 'Kyrgyzstan',
  value: '+996',
  iso: 'KG',
}, {
  country: 'Laos',
  value: '+856',
  iso: 'LA',
}, {
  country: 'Latvia',
  value: '+371',
  iso: 'LV',
}, {
  country: 'Lebanon',
  value: '+961',
  iso: 'LB',
}, {
  country: 'Lesotho',
  value: '+266',
  iso: 'LS',
}, {
  country: 'Liberia',
  value: '+231',
  iso: 'LR',
}, {
  country: 'Libya',
  value: '+218',
  iso: 'LY',
}, {
  country: 'Liechtenstein',
  value: '+423',
  iso: 'LI',
}, {
  country: 'Lithuania',
  value: '+370',
  iso: 'LT',
}, {
  country: 'Luxembourg',
  value: '+352',
  iso: 'LU',
}, {
  country: 'Macao',
  value: '+853',
  iso: 'MO',
}, {
  country: 'Macedonia',
  value: '+389',
  iso: 'MK',
}, {
  country: 'Madagascar',
  value: '+261',
  iso: 'MG',
}, {
  country: 'Malawi',
  value: '+265',
  iso: 'MW',
}, {
  country: 'Malaysia',
  value: '+60',
  iso: 'MY',
}, {
  country: 'Maldives',
  value: '+960',
  iso: 'MV',
}, {
  country: 'Mali',
  value: '+223',
  iso: 'ML',
}, {
  country: 'Malta',
  value: '+356',
  iso: 'MT',
}, {
  country: 'Marshall Islands',
  value: '+692',
  iso: 'MH',
}, {
  country: 'Mauritania',
  value: '+222',
  iso: 'MR',
}, {
  country: 'Mauritius',
  value: '+230',
  iso: 'MU',
}, {
  country: 'Mayotte',
  value: '+262',
  iso: 'YT',
}, {
  country: 'Mexico',
  value: '+52',
  iso: 'MX',
}, {
  country: 'Micronesia',
  value: '+691',
  iso: 'FM',
}, {
  country: 'Moldova',
  value: '+373',
  iso: 'MD',
}, {
  country: 'Monaco',
  value: '+377',
  iso: 'MC',
}, {
  country: 'Mongolia',
  value: '+976',
  iso: 'MN',
}, {
  country: 'Montenegro',
  value: '+382',
  iso: 'ME',
}, {
  country: 'Montserrat',
  value: '+1-664',
  iso: 'MS',
}, {
  country: 'Morocco',
  value: '+212',
  iso: 'MA',
}, {
  country: 'Mozambique',
  value: '+258',
  iso: 'MZ',
}, {
  country: 'Myanmar',
  value: '+95',
  iso: 'MM',
}, {
  country: 'Namibia',
  value: '+264',
  iso: 'NA',
}, {
  country: 'Nauru',
  value: '+674',
  iso: 'NR',
}, {
  country: 'Nepal',
  value: '+977',
  iso: 'NP',
}, {
  country: 'Netherlands',
  value: '+31',
  iso: 'NL',
}, {
  country: 'Netherlands Antilles',
  value: '+599',
  iso: 'AN',
}, {
  country: 'New Caledonia',
  value: '+687',
  iso: 'NC',
}, {
  country: 'New Zealand',
  value: '+64',
  iso: 'NZ',
}, {
  country: 'Nicaragua',
  value: '+505',
  iso: 'NI',
}, {
  country: 'Niger',
  value: '+227',
  iso: 'NE',
}, {
  country: 'Nigeria',
  value: '+234',
  iso: 'NG',
}, {
  country: 'Niue',
  value: '+683',
  iso: 'NU',
}, {
  country: 'North Korea',
  value: '+850',
  iso: 'KP',
}, {
  country: 'Northern Mariana Islands',
  value: '+1-670',
  iso: 'MP',
}, {
  country: 'Norway',
  value: '+47',
  iso: 'NO',
}, {
  country: 'Oman',
  value: '+968',
  iso: 'OM',
}, {
  country: 'Pakistan',
  value: '+92',
  iso: 'PK',
}, {
  country: 'Palau',
  value: '+680',
  iso: 'PW',
}, {
  country: 'Palestine',
  value: '+970',
  iso: 'PS',
}, {
  country: 'Panama',
  value: '+507',
  iso: 'PA',
}, {
  country: 'Papua New Guinea',
  value: '+675',
  iso: 'PG',
}, {
  country: 'Paraguay',
  value: '+595',
  iso: 'PY',
}, {
  country: 'Peru',
  value: '+51',
  iso: 'PE',
}, {
  country: 'Philippines',
  value: '+63',
  iso: 'PH',
}, {
  country: 'Pitcairn',
  value: '+64',
  iso: 'PN',
}, {
  country: 'Poland',
  value: '+48',
  iso: 'PL',
}, {
  country: 'Portugal',
  value: '+351',
  iso: 'PT',
}, {
  country: 'Puerto Rico',
  value: '+1-787, 1-939',
  iso: 'PR',
}, {
  country: 'Qatar',
  value: '+974',
  iso: 'QA',
}, {
  country: 'Republic of the Congo',
  value: '+242',
  iso: 'CG',
}, {
  country: 'Reunion',
  value: '+262',
  iso: 'RE',
}, {
  country: 'Romania',
  value: '+40',
  iso: 'RO',
}, {
  country: 'Russia',
  value: '+7',
  iso: 'RU',
}, {
  country: 'Rwanda',
  value: '+250',
  iso: 'RW',
}, {
  country: 'Saint Barthelemy',
  value: '+590',
  iso: 'BL',
}, {
  country: 'Saint Helena',
  value: '+290',
  iso: 'SH',
}, {
  country: 'Saint Kitts and Nevis',
  value: '+1-869',
  iso: 'KN',
}, {
  country: 'Saint Lucia',
  value: '+1-758',
  iso: 'LC',
}, {
  country: 'Saint Martin',
  value: '+590',
  iso: 'MF',
}, {
  country: 'Saint Pierre and Miquelon',
  value: '+508',
  iso: 'PM',
}, {
  country: 'Saint Vincent and the Grenadines',
  value: '+1-784',
  iso: 'VC',
}, {
  country: 'Samoa',
  value: '+685',
  iso: 'WS',
}, {
  country: 'San Marino',
  value: '+378',
  iso: 'SM',
}, {
  country: 'Sao Tome and Principe',
  value: '+239',
  iso: 'ST',
}, {
  country: 'Saudi Arabia',
  value: '+966',
  iso: 'SA',
}, {
  country: 'Senegal',
  value: '+221',
  iso: 'SN',
}, {
  country: 'Serbia',
  value: '+381',
  iso: 'RS',
}, {
  country: 'Seychelles',
  value: '+248',
  iso: 'SC',
}, {
  country: 'Sierra Leone',
  value: '+232',
  iso: 'SL',
}, {
  country: 'Singapore',
  value: '+65',
  iso: 'SG',
}, {
  country: 'Sint Maarten',
  value: '+1-721',
  iso: 'SX',
}, {
  country: 'Slovakia',
  value: '+421',
  iso: 'SK',
}, {
  country: 'Slovenia',
  value: '+386',
  iso: 'SI',
}, {
  country: 'Solomon Islands',
  value: '+677',
  iso: 'SB',
}, {
  country: 'Somalia',
  value: '+252',
  iso: 'SO',
}, {
  country: 'South Africa',
  value: '+27',
  iso: 'ZA',
}, {
  country: 'South Korea',
  value: '+82',
  iso: 'KR',
}, {
  country: 'South Sudan',
  value: '+211',
  iso: 'SS',
}, {
  country: 'Spain',
  value: '+34',
  iso: 'ES',
}, {
  country: 'Sri Lanka',
  value: '+94',
  iso: 'LK',
}, {
  country: 'Sudan',
  value: '+249',
  iso: 'SD',
}, {
  country: 'Suriname',
  value: '+597',
  iso: 'SR',
}, {
  country: 'Svalbard and Jan Mayen',
  value: '+47',
  iso: 'SJ',
}, {
  country: 'Swaziland',
  value: '+268',
  iso: 'SZ',
}, {
  country: 'Sweden',
  value: '+46',
  iso: 'SE',
}, {
  country: 'Switzerland',
  value: '+41',
  iso: 'CH',
}, {
  country: 'Syria',
  value: '+963',
  iso: 'SY',
}, {
  country: 'Taiwan',
  value: '+886',
  iso: 'TW',
}, {
  country: 'Tajikistan',
  value: '+992',
  iso: 'TJ',
}, {
  country: 'Tanzania',
  value: '+255',
  iso: 'TZ',
}, {
  country: 'Thailand',
  value: '+66',
  iso: 'TH',
}, {
  country: 'Togo',
  value: '+228',
  iso: 'TG',
}, {
  country: 'Tokelau',
  value: '+690',
  iso: 'TK',
}, {
  country: 'Tonga',
  value: '+676',
  iso: 'TO',
}, {
  country: 'Trinidad and Tobago',
  value: '+1-868',
  iso: 'TT',
}, {
  country: 'Tunisia',
  value: '+216',
  iso: 'TN',
}, {
  country: 'Turkey',
  value: '+90',
  iso: 'TR',
}, {
  country: 'Turkmenistan',
  value: '+993',
  iso: 'TM',
}, {
  country: 'Turks and Caicos Islands',
  value: '+1-649',
  iso: 'TC',
}, {
  country: 'Tuvalu',
  value: '+688',
  iso: 'TV',
}, {
  country: 'U.S. Virgin Islands',
  value: '+1-340',
  iso: 'VI',
}, {
  country: 'Uganda',
  value: '+256',
  iso: 'UG',
}, {
  country: 'Ukraine',
  value: '+380',
  iso: 'UA',
}, {
  country: 'United Arab Emirates',
  value: '+971',
  iso: 'AE',
}, {
  country: 'United Kingdom',
  value: '+44',
  iso: 'GB',
}, {
  country: 'United States',
  value: '+1',
  iso: 'US',
}, {
  country: 'Uruguay',
  value: '+598',
  iso: 'UY',
}, {
  country: 'Uzbekistan',
  value: '+998',
  iso: 'UZ',
}, {
  country: 'Vanuatu',
  value: '+678',
  iso: 'VU',
}, {
  country: 'Vatican',
  value: '+379',
  iso: 'VA',
}, {
  country: 'Venezuela',
  value: '+58',
  iso: 'VE',
}, {
  country: 'Vietnam',
  value: '+84',
  iso: 'VN',
}, {
  country: 'Wallis and Futuna',
  value: '+681',
  iso: 'WF',
}, {
  country: 'Western Sahara',
  value: '+212',
  iso: 'EH',
}, {
  country: 'Yemen',
  value: '+967',
  iso: 'YE',
}, {
  country: 'Zambia',
  value: '+260',
  iso: 'ZM',
}, {
  country: 'Zimbabwe',
  value: '+263',
  iso: 'ZW',
}]

export default countryPhoneCodes
