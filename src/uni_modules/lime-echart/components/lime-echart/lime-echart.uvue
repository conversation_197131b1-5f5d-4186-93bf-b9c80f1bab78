<template>
	<view style="width: 100%; height: 408px;">
		<l-echart ref="chartRef" @finished="init"></l-echart>
	</view>
</template>

<script lang="uts" setup>
	// @ts-nocheck
	// #ifndef APP
	import * as echarts from 'echarts/dist/echarts.esm.js'
	// #endif
	const chartRef = ref<LEchartComponentPublicInstance|null>(null)
	const option = {
		tooltip: {
			trigger: 'axis',
			// shadowBlur: 0,
			textStyle: {
				textShadowBlur: 0
			},
			renderMode: 'richText',
		},
		// formatter: async (params: any) => {
		// 	console.log('params', params)
		// 	return 1
		// },
		legend: {
			data: ['邮件营销', '联盟广告', '视频广告', '直接访问', '搜索引擎']
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
		},
		yAxis: {
			type: 'value'
		},
		series: [
			{
				name: '邮件营销',
				type: 'line',
				stack: '总量',
				data: [120, 132, 101, 134, 90, 230, 210]
			},
			{
				name: '联盟广告',
				type: 'line',
				stack: '总量',
				data: [220, 182, 191, 234, 290, 330, 310]
			},
			{
				name: '视频广告',
				type: 'line',
				stack: '总量',
				data: [150, 232, 201, 154, 190, 330, 410]
			},
			{
				name: '直接访问',
				type: 'line',
				stack: '总量',
				data: [320, 332, 301, 334, 390, 330, 320]
			},
			{
				name: '搜索引擎',
				type: 'line',
				stack: '总量',
				data: [820, 932, 901, 934, 1290, 1330, 1320]
			}
		]
	}

	const init = async () =>{
		if(chartRef.value== null) return
		// #ifdef APP
		const chart = await chartRef.value!.init(null)
		// #endif
		// #ifndef APP
		const chart = await chartRef.value!.init(echarts, null)
		// #endif
		chart.setOption(option)
		chart.on('mouseover', function (params) {
		    console.log('params', params);
		});
		
		
		// setTimeout(()=> {
		// 	const option1 = {
		// 		tooltip: {
		// 			trigger: 'axis',
		// 			// shadowBlur: 0,
		// 			textStyle: {
		// 				textShadowBlur: 0
		// 			},
		// 			renderMode: 'richText',
		// 		},
		// 		legend: {
		// 			data: ['邮件营销', '联盟广告', '视频广告', '直接访问', '搜索引擎']
		// 		},
		// 		grid: {
		// 			left: '3%',
		// 			right: '4%',
		// 			bottom: '3%',
		// 			containLabel: true
		// 		},
		// 		xAxis: {
		// 			type: 'category',
		// 			boundaryGap: false,
		// 			data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
		// 		},
		// 		yAxis: {
		// 			type: 'value'
		// 		},
		// 		series: [
		// 			{
		// 				name: '邮件营销',
		// 				type: 'line',
		// 				stack: '总量',
		// 				data: [820, 132, 101, 134, 90, 230, 210]
		// 			},
		// 			{
		// 				name: '联盟广告',
		// 				type: 'line',
		// 				stack: '总量',
		// 				data: [220, 182, 191, 234, 290, 330, 310]
		// 			},
		// 			{
		// 				name: '视频广告',
		// 				type: 'line',
		// 				stack: '总量',
		// 				data: [950, 232, 201, 154, 190, 330, 410]
		// 			},
		// 			{
		// 				name: '直接访问',
		// 				type: 'line',
		// 				stack: '总量',
		// 				data: [320, 332, 301, 334, 390, 330, 320]
		// 			},
		// 			{
		// 				name: '搜索引擎',
		// 				type: 'line',
		// 				stack: '总量',
		// 				data: [820, 932, 901, 934, 1290, 1330, 1320]
		// 			}
		// 		]
		// 	}
		// 	chart.setOption(option1)
		// },1000)
	}
	

</script>
<style>

</style>