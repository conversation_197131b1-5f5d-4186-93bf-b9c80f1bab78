// 音频相关类型定义

interface AudioSourceDetail {
  id: number
  name: string
  master_name: string
  cover: string
  audio: string
  duration: number
  duration_text: string
}

interface AudioManagerMeta {
  id: number
  title: string
  coverImgUrl: string
  epname: string
  singer: string
  src: string
  playState: boolean
  isFirstPlay: boolean
  currentTime: number
  totalDurtaion: number
  totalDurtaionLabel: string
}

// 音频管理器事件类型
enum AudioManagerEvent {
  PlayStateChange = 'audio:playStateChange',
  CurrentTimeChange = 'audio:currentTimeChange',
  PlayNextAudio = 'audio:playNextAudio'
}

// 全局声明
declare global {
  interface AudioSourceDetail {
    id: number
    name: string
    master_name: string
    cover: string
    audio: string
    duration: number
    duration_text: string
  }

  interface AudioManagerMeta {
    id: number
    title: string
    coverImgUrl: string
    epname: string
    singer: string
    src: string
    playState: boolean
    isFirstPlay: boolean
    currentTime: number
    totalDurtaion: number
    totalDurtaionLabel: string
  }

  const AudioManagerEvent: {
    PlayStateChange: string
    CurrentTimeChange: string
    PlayNextAudio: string
  }
}

export {}
