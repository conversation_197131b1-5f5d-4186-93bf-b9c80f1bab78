<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import ReportBox from './components/ReportBox.vue';

defineProps<{
  id: string
}>()

const type = ref<string>('')

onLoad((options) => {
  if (options?.type) {
    type.value = options.type
    console.log('获取到的 type:', options.type)
  }
})

// 儿童人格形象
const detail = ref<any>({
  quest: 'FSET Complete Version（100 Q’s）',
  date: '2025-04-02',
  title: 'Little Comforter',
  subtitle: 'Gentle empathetic type',
  content: 'Always be the first to run to comfort friends when they are hurt or sad.',
})

// 儿童结果详情
const result = ref<any>([
  { title: 'Fire', value: [{ title: 'Performer', value: 43, color: '#EF5875' }, { title: 'Feeler', value: 57, color: '#30B0C7' }] },
  { title: 'Water', value: [{ title: 'Comforter', value: 57, color: '#CD67E1' }, { title: 'Watcher', value: 43, color: '#ECA54B' }] },
  { title: 'Wind', value: [{ title: 'Explorer', value: 43, color: '#32ADE6' }, { title: 'Wanderer', value: 57, color: '#6EE39F' }] },
  { title: 'Earth', value: [{ title: 'Observer', value: 57, color: '#5856D6' }, { title: 'Builder', value: 43, color: '#AF52DE' }] },
])

// 压力报告
const pressure = ref<any>([
  {
    title: 'High-pressure zone (4-5 points)',
    children: ['Stress perception: Significantly higher than the norm mean (3.5 points), reflecting excessive sensitivity to parenting details (such as sleep training, complementary food addition, etc.)', 'Educational cognition: There is a cognitive conflict between traditional educational concepts and modern scientific methods (for example, the idea that "children should not lose at the starting line" affects decision-making)'],
  },
  {
    title: 'Medium-pressure zone (3 points)',
    children: ['Self-regulation: Emotional management ability is at a critical value and is easily affected by sudden parenting events (such as a sharp increase in anxiety levels when a child is ill).'],
  },
  {
    title: 'Low-pressure zone (1-2 points)',
    children: ['Parent-child interaction: The quality of emotional connection is excellent, and the ability to express body language is outstanding (such as the frequency of hugging is 2.3 times higher than that of the normal module).', 'Social support: The community has a strong ability to integrate resources and has established an effective mutual assistance network for parenting (participating in more than three parenting communities).'],
  },
])

// 压力报告优势能力
const advantage = ref<any>([{
  title: 'Emotional connection expert',
  color: '#B1E5FC',
  value: '5',
  content: 'The daily effective interaction time is ≥90 minutes, and the success rate of conflict resolution is 92%',
}, {
  title: 'Emotional connection expert',
  color: '#FFC9A0',
  value: '5',
  content: 'The daily effective interaction time is ≥90 minutes, and the success rate of conflict resolution is 92%',
}, {
  title: 'Emotional connection expert',
  color: '#C1F5D9',
  value: '5',
  content: 'The daily effective interaction time is ≥90 minutes, and the success rate of conflict resolution is 92%',
}])

// 计算背景颜色方法
function getBgColor(item: any) {
  return `linear-gradient(to right, ${item.value[0].color} 0%, ${item.value[0].color} ${item.value[0].value}%, ${item.value[1].color} ${item.value[0].value}%, ${item.value[1].color} 100%)`
}
</script>

<template>
  <view class="page-container app-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Report Detail
        </view>
      </template>
      <template #right>
        <view class="right-slot">
          Share
        </view>
      </template>
    </TopNav>

    <scroll-view scroll-y class="scroll-content" :show-scrollbar="false">
      <!-- 儿童-人物画像 -->
      <ReportBox v-if="type === 'child-modal'">
        <view class="title">
          <view>{{ detail.quest }}</view>
          <view>{{ detail.date }}</view>
        </view>
        <view class="type-box">
          <view class="type-title">
            {{ detail.title }}
          </view>
          <view class="type-subtitle">
            {{ detail.subtitle }}
          </view>
        </view>
        <view class="avatar" />
        <view class="content">
          {{ detail.content }}
        </view>
      </ReportBox>

      <!-- 儿童-result details -->
      <ReportBox v-if="type === 'child-modal'" :subtitle="detail.subtitle" title="Result Details">
        <view class="detail">
          <view v-for="item in result" :key="item.title" class="item">
            {{ item.title }}
            <view
              class="bar" :style="{
                background: getBgColor(item),
              }"
            >
              <view
                v-for="(i, index) in item.value" :key="i.title" class="bar-item" :style="{
                  flexDirection: index === 0 ? 'row' : 'row-reverse',
                }"
              >
                <text>{{ i.title }}</text>
                <text class="line">
                  |
                </text>
                <text>{{ i.title.substring(0, 1) }}</text>
                <text />
                <text>{{ i.value }}%</text>
              </view>
            </view>
          </view>
        </view>
      </ReportBox>

      <!-- 压力报告 -->
      <ReportBox v-if="type === 'father-modal'">
        <view class="pressure" />
        <view class="pressure-container">
          <view v-for="(item, index) in pressure" :key="item.title" class="pressure-container">
            <view class="pressure-title">
              {{ index + 1 }}. {{ item.title }}
            </view>
            <view v-for="(i) in item.children" :key="i" class="pressure-content">
              <view>·</view>{{ i }}
            </view>
          </view>
        </view>
      </ReportBox>

      <!-- Advantage capability matrix -->
      <ReportBox title="Advantage capability matrix">
        <view v-for="(item, index) in advantage" :key="index" class="advantage-item">
          <view class="advantage-sign" :style="{ background: item.color }" />
          <view class="advantage-content">
            <view class="advantage-title">
              <view>
                {{ item.title }}
              </view>
              <view class="advantage-rank" :style="{ background: item.color }">
                The top {{ item.value }}%
              </view>
            </view>
            <view class="advantage-text">
              {{ item.content }}
            </view>
          </view>
        </view>
      </ReportBox>

      <!-- Personality Profile -->
      <ReportBox
        :subtitle="detail.subtitle" title="Personality Profile"
        content="People of the Gentle empathetic type are known for their idealism and profound emotional experiences. They are usually introverted thinkers, with a rich inner world and profound feelings about beautiful things. Infps pursue personal authenticity and self-consistency, and are particularly passionate about careers that align with their values. They are particularly outstanding in expressing creativity and imagination, and are good at exploring and expressing complex human emotions through art and writing. INFP usually holds a thoughtful view of interpersonal relationships and seeks to establish true connections with others."
      />

      <view class="bottom" />
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .right-slot {
    text-align: right;
    color: #98a2b3;
    font-size: $font-size-md;
  }

  .scroll-content {
    flex: 1;
    overflow: hidden;

    .shadow {
      border: 1px solid #FFCAA1;
      box-shadow: 6px 6px 24px 0px #F0914426;
      padding: 40rpx;
      box-sizing: border-box;
      width: 686rpx;
      border-radius: 40rpx;
      margin: 0 auto;
    }

    .avatar-container {
      margin-top: 40rpx;
      display: flex;
      flex-direction: column;
      gap: 40rpx;
      align-items: center;

      .title {
        display: flex;
        justify-content: space-between;
        font-size: $font-size-base;
        color: #667085;
        font-family: var(--font-family-light);
        font-weight: $font-weight-light;
        line-height: 100%;
        letter-spacing: 2%;
        margin: 0 auto;
        width: 100%;
      }

      .type-box {
        width: 550rpx;
        padding: 0 16rpx;
        box-sizing: border-box;

        .type-title {
          text-align: left;
          font-family: var(--font-family-title);
          font-weight: $font-weight-bold;
          font-size: $font-size-heading-xl;
          line-height: 100%;
          vertical-align: middle;

          background: $gradient-color-three-element;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;

          /* 替代 text-shadow */
          filter: drop-shadow(2px 2px rgba(48, 57, 117, 0.3));
        }

        .type-subtitle {
          text-align: right;
          margin-top: 16rpx;

          font-family: var(--font-family-regular);
          font-weight: $font-weight-regular;
          font-size: $font-size-heading-base;
          color: #1d232e;
          line-height: 100%;
          vertical-align: middle;

        }
      }

      .avatar {
        width: 472rpx;
        height: 472rpx;
        background-color: #f9fafb;
      }

      .content {
        font-family: var(--font-family-regular);
        font-weight: $font-weight-regular;
        font-size: $font-size-md;
        line-height: 150%;
        vertical-align: middle;
        text-shadow: 1px 1px 1px 0px #0000001A;
        color: #667085;
      }
    }

    .detail {
      display: flex;
      flex-direction: column;
      gap: 40rpx;

      .item {
        width: 606rpx;
        height: 118rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        font-family: var(--font-family-regular);
        font-weight: $font-weight-regular;
        font-size: $font-size-md;
        line-height: 150%;
        text-align: center;
        vertical-align: middle;
        color: #3c4353;

        .bar {
          height: 60rpx;
          width: 100%;
          border-radius: 100rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 40rpx;
          box-sizing: border-box;

          .bar-item {
            display: flex;
            gap: 12rpx;

            .line {
              color: #FFFFFF4D;
            }

            text {
              text-shadow: 1px 1px 1px 0px #0000001A;
              color: #fff;
              font-size: $font-size-base;
              line-height: 150%;
              vertical-align: middle;
            }
          }
        }
      }
    }

    .pressure {
      width: 606rpx;
      height: 606rpx;
      background-color: aqua;
      border-radius: 40rpx;
      border: 1px solid #131323;
      box-sizing: border-box;
    }

    .pressure-container {
      display: flex;
      flex-direction: column;
      font-family: var(--font-family-regular);
      gap: 20rpx;
      font-weight: $font-weight-regular;
      font-size: $font-size-md;
      line-height: 158%;
      text-align: justify;
      vertical-align: middle;

      .pressure-content {
        font-family: var(--font-family-light);
        font-weight: $font-weight-light;
        display: flex;

        view {
          padding: 0 16rpx;
          font-family: var(--font-family-title);
        }
      }
    }

    .advantage-item {
      width: 606rpx;
      display: flex;
      min-height: 160rpx;
      border-radius: 20rpx;
      border: 1px solid #edf2f7;
      overflow: hidden;
      box-shadow: 0px 0px 16px 2px #0000000D;

      .advantage-sign {
        width: 18rpx;
        // height: 160rpx;
        height: auto;
      }

      .advantage-content {
        flex: 1;
        padding: 24rpx 32rpx;

        .advantage-title {
          display: flex;
          justify-content: space-between;
          font-family: var(--font-family-title);
          font-weight: $font-weight-bold;
          font-size: $font-size-base;
          color: #101828;
          line-height: 150%;
          vertical-align: middle;
          height: 40rpx;
          align-items: center;

          .advantage-rank {
            height: 40rpx;
            line-height: 40rpx;
            border-radius: 8rpx;
            padding: 0 8rpx;
            font-family: var(--font-family-regular);
            font-weight: $font-weight-medium;
            font-size: $font-size-xxls;
            text-align: center;
            vertical-align: middle;
            color: #3c4353;
          }
        }

        .advantage-text {
          margin-top: 12rpx;
          width: 100%;
          font-family: var(--font-family-regular);
          font-weight: $font-weight-regular;
          font-size: $font-size-xs;
          line-height: 150%;
          letter-spacing: 3%;
          vertical-align: middle;
          color: #667085;
        }
      }
    }
  }
}

.bottom {
  height: 40rpx;
}
</style>
