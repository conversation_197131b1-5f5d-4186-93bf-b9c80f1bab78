<template>
  <view class="page-container app-container">
    <TopNav icon="back" color="#fff">
      <template #middle>
        <view class="middle-slot">
          Bedtime stories
        </view>
      </template>
    </TopNav>

    <scroll-view class="story-container" scroll-y :show-scrollbar="false">
      <view class="story-content">
        <!-- 故事封面图片 -->
        <view class="story-img">
          <image :src="currentStory.cover" mode="scaleToFill" />
        </view>

        <!-- 故事标题和作者 -->
        <view class="story-info">
          <view class="story-title">{{ currentStory.title }}</view>
          <view class="story-author">{{ currentStory.author }}</view>
        </view>

        <!-- 播放进度条 -->
        <view class="progress-container">
          <view class="time-info">
            <text class="current-time">{{ formatTime(audioManagerMeta.currentTime) }}</text>
            <text class="total-time">-{{ formatTime(audioManagerMeta.totalDurtaion - audioManagerMeta.currentTime) }}</text>
          </view>

          <view class="progress-bar" @touchstart="progressTouchStartHandler" @touchmove="progressTouchMoveHandler" @touchend="progressTouchEndHandler">
            <view class="progress-track">
              <view class="progress-active" :style="{ width: progressActiveLineWidth + 'rpx' }"></view>
            </view>
          </view>
        </view>

        <!-- 播放控制按钮 -->
        <view class="control-buttons">
          <view class="control-btn" @click="backward(15)">
            <wd-icon name="refresh" size="48rpx" color="#fff" />
          </view>

          <view class="control-btn" @click="backward(10)">
            <wd-icon name="arrow-left" size="48rpx" color="#fff" />
          </view>

          <view class="play-btn" @click="togglePlay">
            <wd-icon :name="audioManagerMeta.playState ? 'pause' : 'play'" size="64rpx" color="#fff" />
          </view>

          <view class="control-btn" @click="forward(10)">
            <wd-icon name="arrow-right" size="48rpx" color="#fff" />
          </view>

          <view class="control-btn" @click="toggleShuffle">
            <wd-icon name="refresh" size="48rpx" color="#fff" />
          </view>
        </view>

        <!-- 音量控制 -->
        <view class="volume-container">
          <wd-icon name="volume-off" size="32rpx" color="#fff" />
          <view class="volume-slider">
            <view class="volume-track">
              <view class="volume-active" :style="{ width: volume + '%' }"></view>
            </view>
          </view>
          <wd-icon name="volume-up" size="32rpx" color="#fff" />
        </view>

        <!-- 歌词显示区域 -->
        <view class="lyrics-container">
          <view class="lyrics-title">Lyrics</view>
          <view class="lyrics-content">
            <text>{{ currentStory.lyrics }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAudioStore } from '@/stores/audioManager'

// 音频管理器
const audioStore = useAudioStore()
const {
  audioManagerMeta,
  progressActiveLineWidth,
  play,
  pause,
  forward,
  backward,
  setAudioMetaInfo,
  setAudioManagerInfo,
  setAudioSourceDetail,
  progressTouchStartHandler,
  progressTouchMoveHandler,
  progressTouchEndHandler
} = audioStore

// 音量控制
const volume = ref(70)

// 模拟故事数据
const currentStory = ref({
  id: 1,
  title: 'Like it Doesn\'t Hurt',
  author: 'Chris shelet',
  cover: 'https://picsum.photos/590/590',
  audio: 'https://music.163.com/song/media/outer/url?id=25906124.mp3', // 示例音频链接
  duration: 208, // 3:28 in seconds
  duration_text: '3:28',
  lyrics: `When the last ray of the setting sun
disappeared in the flower field,
five hundred fireflies lit up their little lanterns
simultaneously.

The golden-green spots of light, like dancing
dewdrops,
wove a flowing net of light in the night wind -
except for the small awns curled up on the back
of the sunflowers, trembling.

Is your lantern broken again?
The companions deliberately circled around
and asked with concern.

"No, it's not broken," the little firefly
whispered softly, "I just... I just don't want
to shine anymore."

The night was so quiet that you could hear
the dewdrops falling on the petals.`
})

// 时间格式化函数
function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 播放/暂停切换
function togglePlay() {
  if (audioManagerMeta.playState) {
    pause()
  } else {
    play()
  }
}

// 随机播放切换
function toggleShuffle() {
  // 实现随机播放逻辑
  console.log('Toggle shuffle')
}

// 初始化音频
onMounted(() => {
  try {
    // 设置音频信息
    const storyDetail = {
      id: currentStory.value.id,
      name: currentStory.value.title,
      master_name: currentStory.value.author,
      cover: currentStory.value.cover,
      audio: currentStory.value.audio,
      duration: currentStory.value.duration,
      duration_text: currentStory.value.duration_text
    }

    setAudioSourceDetail(storyDetail)
    setAudioMetaInfo(storyDetail)
    setAudioManagerInfo()

    console.log('Audio initialized:', storyDetail)
  } catch (error) {
    console.error('Failed to initialize audio:', error)
    uni.showToast({
      title: 'Failed to load story',
      icon: 'none'
    })
  }
})

onUnmounted(() => {
  // 清理资源
  try {
    // 可以在这里停止音频播放
    // pause()
  } catch (error) {
    console.error('Error during cleanup:', error)
  }
})
</script>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;
  background-image: url('/static/accompany/storyBg.png');
}

.middle-slot {
  color: #fff;
}

.story-container {
  flex: 1;
  overflow: hidden;
  font-family: var(--font-family-regular);

  .story-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 32rpx;
  }

  .story-img {
    margin-top: 60rpx;
    width: 590rpx;
    height: 590rpx;
    border-radius: 40rpx;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);

    image {
      width: 100%;
      height: 100%;
    }
  }

  .story-info {
    margin-top: 60rpx;
    text-align: center;
    color: #fff;

    .story-title {
      font-family: var(--font-family-title);
      font-size: $font-size-heading-md;
      font-weight: $font-weight-bold;
      margin-bottom: 16rpx;
    }

    .story-author {
      font-family: var(--font-family-regular);
      font-size: $font-size-lg;
      opacity: 0.8;
    }
  }

  .progress-container {
    width: 100%;
    margin-top: 80rpx;
    color: #fff;

    .time-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      font-size: $font-size-md;
      opacity: 0.8;
    }

    .progress-bar {
      width: 100%;
      height: 60rpx;
      display: flex;
      align-items: center;
      padding: 0 20rpx;
      box-sizing: border-box;

      .progress-track {
        flex: 1;
        height: 8rpx;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4rpx;
        position: relative;

        .progress-active {
          height: 100%;
          background: #fff;
          border-radius: 4rpx;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            right: -12rpx;
            top: 50%;
            transform: translateY(-50%);
            width: 24rpx;
            height: 24rpx;
            background: #fff;
            border-radius: 50%;
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }

  .control-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 60rpx;
    margin-top: 80rpx;

    .control-btn {
      width: 96rpx;
      height: 96rpx;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10rpx);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        background: rgba(255, 255, 255, 0.3);
      }
    }

    .play-btn {
      width: 128rpx;
      height: 128rpx;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }

      :deep(.wd-icon) {
        color: #8B7355 !important;
      }
    }
  }

  .volume-container {
    display: flex;
    align-items: center;
    gap: 24rpx;
    margin-top: 60rpx;
    width: 100%;
    color: #fff;
    opacity: 0.8;

    .volume-slider {
      flex: 1;
      height: 40rpx;
      display: flex;
      align-items: center;

      .volume-track {
        flex: 1;
        height: 6rpx;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3rpx;
        position: relative;

        .volume-active {
          height: 100%;
          background: #fff;
          border-radius: 3rpx;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            right: -10rpx;
            top: 50%;
            transform: translateY(-50%);
            width: 20rpx;
            height: 20rpx;
            background: #fff;
            border-radius: 50%;
          }
        }
      }
    }
  }

  .lyrics-container {
    width: 100%;
    margin-top: 60rpx;
    margin-bottom: 60rpx;

    .lyrics-title {
      color: #fff;
      font-family: var(--font-family-title);
      font-size: $font-size-heading-base;
      font-weight: $font-weight-bold;
      margin-bottom: 32rpx;
    }

    .lyrics-content {
      background: rgba(139, 115, 85, 0.6);
      border-radius: 32rpx;
      padding: 48rpx 32rpx;
      backdrop-filter: blur(10rpx);

      text {
        color: #fff;
        font-family: var(--font-family-regular);
        font-size: $font-size-lg;
        line-height: 1.8;
        opacity: 0.9;
      }
    }
  }
}
</style>