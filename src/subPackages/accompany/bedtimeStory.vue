<template>
  <view class="page-container app-container">
    <TopNav icon="back" color="#fff">
      <template #middle>
        <view class="middle-slot">
          Bedtime stories
        </view>
      </template>
    </TopNav>

    <scroll-view class="story-container" scroll-y :show-scrollbar="false">
      <view class="story-content">

        <view class="story-img">
          <image src="https://picsum.photos/590/590" mode="scaleToFill" />
        </view>
      </view>
    </scroll-view>

  </view>
</template>

<script setup lang="ts">

</script>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;
  background-image: url('/static/accompany/storyBg.png');
  border-radius: 8rpx;
}

.middle-slot {
  color: #fff;
}

.story-container {
  flex: 1;
  overflow: hidden;
  font-family: var(--font-family-regular);

  .story-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .story-img {
    padding-top: 104rpx;
    width: 590rpx;
    height: 590rpx;

    image {
      width: 590rpx;
      height: 590rpx;
    }
  }
}
</style>