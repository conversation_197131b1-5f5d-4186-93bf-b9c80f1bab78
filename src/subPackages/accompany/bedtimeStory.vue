<template>
  <view class="page-container app-container">
    <TopNav icon="back" color="#fff">
      <template #middle>
        <view class="middle-slot">
          Bedtime stories
        </view>
      </template>
    </TopNav>

    <scroll-view class="story-container" scroll-y :show-scrollbar="false">
      <view class="story-content">

        <view class="story-img">
          <image :src="audioManagerMetaRef.coverImgUrl" mode="scaleToFill" />
        </view>

        <view class="story-box">
          <view class="story-title">{{ audioManagerMetaRef.title }}</view>
          <view class="story-singer">{{ audioManagerMetaRef.singer }}</view>

          <!-- 进度条 -->
          <AudioSlider />
        </view>
      </view>
    </scroll-view>

  </view>
</template>

<script setup lang="ts">
import { useAudioStore } from '@/stores/audioManager';
import { storeToRefs } from 'pinia';
import AudioSlider from './components/AudioSlider.vue';

const audioStore = useAudioStore()
const {
  audioManagerMeta: audioManagerMetaRef,
} = storeToRefs(audioStore)
</script>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;
  background-image: url('/static/accompany/storyBg.png');
  border-radius: 8rpx;
}

.middle-slot {
  color: #fff;
}

.story-container {
  flex: 1;
  overflow: hidden;
  font-family: var(--font-family-regular);

  .story-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .story-img {
    padding-top: 104rpx;
    width: 590rpx;
    height: 590rpx;

    image {
      width: 590rpx;
      height: 590rpx;
      border-radius: 8rpx;
      border: 1px solid aqua;
    }
  }

  .story-box {
    width: 590rpx;
    margin-top: 124rpx;
    color: #fff;
    font-family: var(--font-family-regular);

    .story-title {
      font-weight: $font-weight-medium;
      font-size: $font-size-heading-mdx;
      line-height: 56rpx;
      vertical-align: middle;
    }

    .story-singer {
      margin-top: 16rpx;
      font-weight: $font-weight-regular;
      font-size: $font-size-md;
      line-height: 40rpx;
      vertical-align: middle;

    }
  }
}
</style>