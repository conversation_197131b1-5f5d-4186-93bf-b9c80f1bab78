<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { onMounted } from 'vue'
import { useAudioStore } from '@/stores/audioManager'
import AudioSlider from './components/AudioSlider.vue'
import AudioTool from './components/AudioTool.vue'
import VoiceSlider from './components/VoiceSlider.vue'

const audioStore = useAudioStore()
const {
  audioManagerMeta: audioManagerMetaRef,
} = storeToRefs(audioStore)

const {
  setAudioMetaInfo,
  setAudioManagerInfo,
  setAudioSourceDetail,
} = audioStore

/**
 * 初始化音频数据
 * 设置测试音频的详细信息
 */
function initAudioData() {
  // 使用网络音频进行测试，确保音频可以正常播放
  const testAudioDetail = {
    id: 1,
    name: 'Like it Doesn\'t Hurt',
    master_name: '<PERSON> shelet',
    cover: 'https://picsum.photos/590/590',
    // 使用一个可靠的测试音频URL
    audio: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
    duration: 155,
    duration_text: '02:35',
  }

  try {
    console.log('=== 开始初始化音频数据 ===')
    console.log('测试音频详情:', testAudioDetail)

    // 设置音频源详情
    setAudioSourceDetail(testAudioDetail)
    console.log('音频源详情设置完成')

    // 设置音频元数据
    setAudioMetaInfo(testAudioDetail)
    console.log('音频元数据设置完成')

    // 设置音频管理器信息
    setAudioManagerInfo()
    console.log('音频管理器信息设置完成')

    console.log('音频数据初始化成功:', testAudioDetail)

    // 显示初始化成功提示
    uni.showToast({
      title: '音频初始化成功',
      icon: 'success',
      duration: 2000,
    })
  }
  catch (error) {
    console.error('音频数据初始化失败:', error)
    uni.showToast({
      title: '音频初始化失败',
      icon: 'none',
    })
  }
}

// 页面挂载时初始化音频数据
onMounted(() => {
  initAudioData()
})
</script>

<template>
  <view class="page-container">
    <TopNav icon="back" color="#fff">
      <template #middle>
        <view class="middle-slot">
          Bedtime stories
        </view>
      </template>
    </TopNav>

    <scroll-view class="story-container" scroll-y :show-scrollbar="false">
      <view class="story-content">
        <view class="story-img">
          <image :src="audioManagerMetaRef?.coverImgUrl" mode="scaleToFill" />
        </view>

        <view class="story-box">
          <view class="story-title">
            {{ audioManagerMetaRef?.title }}
          </view>
          <view class="story-singer">
            {{ audioManagerMetaRef?.singer }}
          </view>

          <!-- 进度条 -->
          <AudioSlider />

          <!-- 播放器 -->
          <AudioTool />

          <!-- 音频控制器 -->
          <VoiceSlider />
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;
  background-image: url('/static/accompany/storyBg.png');
  background-size: 100% 100%;
  border-radius: 8rpx;
}

.middle-slot {
  color: #fff;
}

.story-container {
  flex: 1;
  overflow: hidden;
  font-family: var(--font-family-regular);

  .story-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .story-img {
    padding-top: 104rpx;
    width: 590rpx;
    height: 590rpx;

    image {
      width: 590rpx;
      height: 590rpx;
      border-radius: 8rpx;
    }
  }

  .story-box {
    width: 652rpx;
    margin-top: 124rpx;
    color: #fff;
    font-family: var(--font-family-regular);

    .story-title {
      padding-left: 28rpx;
      font-weight: $font-weight-medium;
      font-size: $font-size-heading-mdx;
      line-height: 56rpx;
      vertical-align: middle;
    }

    .story-singer {
      padding-left: 28rpx;
      margin-top: 16rpx;
      font-weight: $font-weight-regular;
      font-size: $font-size-md;
      line-height: 40rpx;
      vertical-align: middle;

    }
  }
}
</style>
