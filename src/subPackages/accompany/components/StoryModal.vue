<script setup lang="ts">
import { computed, ref } from 'vue'

const props = defineProps<{
  type: 'child-modal' | 'mother-modal' | 'father-modal'
}>()
const emit = defineEmits<{
  (e: 'report'): void
  (e: 'hide'): void
}>()

// 不同类型的弹窗数据
const introduceMap = ref<any>({
  'child-modal': {
    title: 'Children Evaluation',
    qs: 100,
    xp: 50,
    total: '267092',
    content: `This test is designed for children aged 3 to 12. Through multi-dimensional behavioral observation, it scientifically assesses children's personality traits and potential for development.\n\nThe test is based on the Four Images-Eight Type Theory and Child Development Psychology, combined with AI algorithm analysis. It constructs an assessment system from four dimensions: social inclination (introverted / extroverted), energy management (practical / intuitive), cognitive style (logical / creative), and behavioral pattern (active / cautious). It generates personalized personality profiles and development suggestions. The test results can assist parents and teachers in formulating educational plans and matching suitable learning methods and interest cultivation paths. The system adopts a dynamic assessment model, regularly updates data, tracks the trajectory of personality development, and helps children build confidence and optimize social skills during critical growth periods. The entire test process is presented in a child-friendly manner to ensure fun and safety, facilitating a comprehensive understanding of children's inner traits and achieving individualized education.`,
  },
  'mother-modal': [
    {
      img: '/static/accompany/Compass.png',
      title: 'Compass Smart',
      qs: 25,
      finished: 2,
      xp: 15,
      id: 1
    },
    {
      img: '/static/accompany/Compass.png',
      title: 'Best Target',
      qs: 25,
      finished: 5,
      xp: 15,
      id: 2
    }
  ],
  'father-modal': {
    title: 'Parent Evaluation',
    qs: 30,
    xp: 15,
    total: '267092',
    content: `This test is designed to assist parents in scientifically assessing the psychological stress and emotional states during the parenting process. It encompasses five core dimensions: \n\nPressure perception: Judging anxiety index through daily scenarios\nParent-child interaction: Assessing communication quality and emotional connection\nSelf-regulation: Detecting emotional management ability\nEducational cognition: Identifying the scientific nature of parenting concepts\nSocial support: Measuring the ability to acquire resources\nBased on the universally accepted principles for compiling psychological scales, all 20 situational-choice questions have undergone reliability and validity tests. After answering, a personalized analysis report is generated immediately, including stress level assessment, identification of strengths and capabilities, and customized improvement suggestions. The test results are for reference only and aim to help parents establish self-awareness. It is recommended to conduct regular re-tests every month to observe the improvement trajectory. Please remain in a natural state when using it and do not deliberately choose the ideal answers.`,
  },
})
const introduce = computed(() => introduceMap.value[props.type])

function handleStart(id?: number) {
  emit('hide')
  uni.navigateTo({
    url: `/subPackages/accompany/evaluation?type=${props.type}&id=${id}`,
  })
}

function handleReport() {
  emit('hide')
  emit('report')
}
</script>

<template>
  <view class="story-modal" :style="{ padding: type === 'mother-modal' ? '56rpx 32rpx' : '56rpx 48rpx' }">
    <template v-if="props.type !== 'mother-modal'">
      <!-- title -->
      <view class="title">
        {{ introduce.title }}
      </view>

      <view class="qs-xp">
        <view class="qs">
          {{ introduce.qs }} Q's
        </view>
        <view class="divide" />
        <view class="xp">
          {{ introduce.xp }} XP
        </view>
      </view>

      <!-- 内容 -->
      <div class="content" v-html="introduce.content" />

      <!-- 按钮 -->
      <view class="btn-box">
        <wd-button custom-class="btn-item btn-start" @click="handleStart">
          Start
        </wd-button>
        <wd-button custom-class="btn-item btn-report" type="text" @click="handleReport">
          Report
        </wd-button>
      </view>

      <!-- 统计 -->
      <view class="statistics">
        A total of {{ Number(introduce.total).toLocaleString('en-US') }} people have taken this test,
      </view>
    </template>
    <template v-else>
      <scroll-view scroll-y class="story-list" :show-scrollbar="false">

        <view class="story-item" v-for="item in introduce" :key="item.title" @click="handleStart(item.id)">
          <view class="story-badge">
            <image :src="item.img" mode="scaleToFill" />
          </view>
          <view class="story-content">
            <view class="story-title">
              {{ item.title }}
            </view>
            <view class="story-progress">
              <view>
                <text v-if="item.finished !== item.qs">{{ item.finished }}/</text>{{ item.qs }} Q’s
              </view>
              <view class="delete"></view>
              <view class="story-XP">
                {{ item.xp }} XP
              </view>
            </view>
          </view>
          <view class="arrow">
            <wd-icon name="arrow-right" size="42rpx" color="#616161"></wd-icon>
          </view>
        </view>
      </scroll-view>
    </template>
  </view>
</template>

<style scoped lang="scss">
.story-modal {
  width: 598rpx;
  height: 800rpx;
  background-color: #fff;
  box-shadow: 10px 20px 46px 0px #9E774D24;
  border-radius: 32rpx;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 56rpx;
  box-sizing: border-box;
  font-family: var(--font-family-title);
  text-align: center;

  .title {
    height: 58rpx;
    line-height: 58rpx;
    color: #212121;
    font-size: $font-size-heading-sm;
    font-weight: $font-weight-bold;
  }

  .qs-xp {
    display: flex;
    gap: 16rpx;
    color: #616161;
    justify-content: center;
    align-items: center;
    font-size: $font-size-md;

    .divide {
      width: 10rpx;
      height: 10rpx;
      background-color: #616161;
      border-radius: 50%;
    }

    .qs {
      font-weight: $font-weight-bold;
    }

    .xp {
      font-weight: $font-weight-regular;
      font-family: var(--font-family-regular);
    }
  }

  .content {
    overflow: auto;
    flex: 1;
    text-align: left;
    font-family: var(--font-family-light);
    font-size: $font-size-md;
    color: #1d232e;
    font-weight: $font-weight-light;
    white-space: pre-line;
    line-height: 150%;
    letter-spacing: 2%;
    vertical-align: middle;

    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
      color: transparent;
    }

    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
  }

  .btn-box {
    height: 220rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .btn-item {
      width: 100%;
      height: 100rpx;
      font-family: var(--font-family-regular);
      font-weight: $font-weight-regular;
      font-size: $font-size-heading-sm;
      line-height: 100%;
    }

    .btn-start {
      box-shadow: 0px 8px 16px 0px #FDA05559;
      background: $gradient-color-light;
    }

    .btn-report {
      color: $color-primary;
      text-shadow: 1px 1px 1px 0px #0000001A;
    }
  }

  .statistics {
    height: 32rpx;
    font-family: var(--font-family-light);
    font-size: $font-size-sm;
    color: #1d232e;
    font-weight: $font-weight-light;
  }

  .story-list {
    width: 100%;
    min-height: 100%;

    .story-item {
      height: 200rpx;
      width: 100%;
      margin-bottom: 56rpx;
      display: flex;
      align-items: center;

      .story-badge {
        width: 200rpx;
        height: 200rpx;
        border-radius: 100rpx;

        image {
          width: 200rpx;
          height: 200rpx;
        }
      }

      .story-content {
        width: 236rpx;
        // height: 44rpx;
        margin-left: 40rpx;
        text-align: left;

        .story-title {
          font-family: var(--font-family-title);
          font-weight: $font-weight-bold;
          font-size: $font-size-heading-sm;
          color: #212121;
          line-height: 160%;
          padding-bottom: 16rpx;
        }

        .story-progress {
          font-size: $font-size-md;
          color: #616161;
          line-height: 140%;
          display: flex;
          align-items: center;
          gap: 16rpx;

          .delete {
            width: 10rpx;
            height: 10rpx;
            border-radius: 50%;
            background-color: #616161;
          }

          .story-XP {
            font-size: $font-size-md;
            font-family: var(--font-family-regular);
            font-weight: $font-weight-regular;
          }
        }
      }

      .arrow {
        margin-left: 16rpx;
      }
    }
  }
}
</style>
