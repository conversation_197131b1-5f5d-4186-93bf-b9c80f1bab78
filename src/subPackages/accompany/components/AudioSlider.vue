<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useAudioStore } from '@/stores/audioManager'

const sliderWrapper = ref<HTMLElement | null>(null)
const audioStore = useAudioStore()
const {
  audioManagerMeta: audioManagerMetaRef,
} = storeToRefs(audioStore)

const { seek } = audioStore

// 本地进度值，用于拖拽时的临时显示
const localProgress = ref(0)
// 是否正在拖拽
const isDragging = ref(false)

// 计算显示的进度值
const displayProgress = computed(() => {
  return isDragging.value ? localProgress.value : audioManagerMetaRef.value.currentTime
})

// 转换数据格式化
function formatSecondsToMMSS(totalSeconds: number): string {
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
}

/**
 * 滑块开始拖拽事件
 * @param event 拖拽事件
 */
function handleDragStart(event: any) {
  console.log('=== 开始拖拽进度条 ===')
  isDragging.value = true
  localProgress.value = event.value
  console.log('拖拽开始，当前值:', event.value)
}

/**
 * 滑块拖拽中事件
 * @param event 拖拽事件
 */
function handleDragMove(event: any) {
  if (isDragging.value) {
    localProgress.value = event.value
    console.log('拖拽中，当前值:', event.value)
  }
}

/**
 * 滑块拖拽结束事件
 * @param event 拖拽事件
 */
function handleDragEnd(event: any) {
  console.log('=== 结束拖拽进度条 ===')
  console.log('拖拽结束，最终值:', event.value)

  try {
    // 跳转到指定位置
    const targetTime = Number(event.value)
    console.log('跳转到时间位置:', targetTime)

    // 调用seek方法跳转播放位置
    seek(targetTime)

    console.log('播放位置跳转完成')

    // 显示跳转提示
    uni.showToast({
      title: `跳转到 ${formatSecondsToMMSS(Math.floor(targetTime))}`,
      icon: 'none',
      duration: 1000,
    })
  }
  catch (error) {
    console.error('跳转播放位置失败:', error)
    uni.showToast({
      title: '跳转失败',
      icon: 'none',
    })
  }
  finally {
    // 重置拖拽状态
    isDragging.value = false
  }
}

/**
 * 滑块值改变事件（点击进度条时触发）
 * @param event 改变事件
 */
function handleChange(event: any) {
  console.log('=== 进度条值改变 ===')
  console.log('新值:', event.value)

  // 如果不是拖拽状态，说明是点击进度条
  if (!isDragging.value) {
    try {
      const targetTime = Number(event.value)
      console.log('点击跳转到时间位置:', targetTime)

      // 跳转播放位置
      seek(targetTime)

      console.log('点击跳转完成')

      // 显示跳转提示
      uni.showToast({
        title: `跳转到 ${formatSecondsToMMSS(Math.floor(targetTime))}`,
        icon: 'none',
        duration: 1000,
      })
    }
    catch (error) {
      console.error('点击跳转失败:', error)
    }
  }
}

function preventScroll(e: TouchEvent) {
  e.preventDefault()
}

onMounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.addEventListener('touchmove', preventScroll, { passive: false })
  }
})

onUnmounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.removeEventListener('touchmove', preventScroll)
  }
})
</script>

<template>
  <view ref="sliderWrapper" class="slider-wrapper no-scroll">
    <wd-slider
      :model-value="displayProgress"
      hide-label
      hide-min-max
      :max="audioManagerMetaRef.totalDurtaion"
      custom-class="audio-slider"
      active-color="#FFFFFF"
      inactive-color="#5f554b"
      @dragstart="handleDragStart"
      @dragmove="handleDragMove"
      @dragend="handleDragEnd"
      @change="handleChange"
    />
    <view class="time-wrapper">
      <view>{{ formatSecondsToMMSS(displayProgress) }}</view>
      <view>{{ audioManagerMetaRef.totalDurtaionLabel }}</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.no-scroll {
  touch-action: none;
  overflow: hidden;
}

.slider-wrapper {
  width: 590rpx;
  padding-left: 28rpx;
}

.audio-slider {
  --wot-slider-handle-bg: transparent;
  --wot-slider-axie-bg: transparent;
  --wot-slider-axie-height: 10rpx;

  :deep(.wd-slider__button) {
    opacity: 0 !important;
    box-shadow: none !important;
  }
}

.time-wrapper {
  // width: 590rpx;
  // padding-left: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--font-family-regular);
  font-weight: $font-weight-regular;
  font-size: $font-size-base;
  line-height: 18px;
  vertical-align: middle;
  position: relative;
  bottom: 10rpx;
}
</style>
