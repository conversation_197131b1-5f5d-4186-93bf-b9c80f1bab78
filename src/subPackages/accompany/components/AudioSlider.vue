<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { useAudioStore } from '@/stores/audioManager'

const sliderWrapper = ref<HTMLElement | null>(null)
const audioStore = useAudioStore()
const {
  audioManagerMeta: audioManagerMetaRef,
} = storeToRefs(audioStore)

const { seek } = audioStore

// 本地进度值，用于拖拽时的临时显示
const localProgress = ref(0)
// 是否正在拖拽
const isDragging = ref(false)

// 计算显示的进度值
const displayProgress = computed(() => {
  return isDragging.value ? localProgress.value : audioManagerMetaRef.value.currentTime
})

// 转换数据格式化
function formatSecondsToMMSS(totalSeconds: number): string {
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
}

/**
 * 滑块开始拖拽事件
 * @param event 拖拽事件 格式: { value }
 */
function handleDragStart(event: { value: number }) {
  console.log('=== 开始拖拽进度条 ===')
  console.log('拖拽开始事件:', event)
  isDragging.value = true
  localProgress.value = event.value
  console.log('拖拽开始，当前值:', event.value)
}

/**
 * 滑块拖拽中事件
 * @param event 拖拽事件 格式: { value }
 */
function handleDragMove(event: { value: number }) {
  console.log('拖拽中事件:', event)
  if (isDragging.value) {
    localProgress.value = event.value
    console.log('拖拽中，当前值:', event.value)
  }
}

/**
 * 滑块拖拽结束事件
 * @param event 拖拽事件 格式: { value }
 */
function handleDragEnd(event: { value: number }) {
  console.log('=== 结束拖拽进度条 ===')
  console.log('拖拽结束事件:', event)
  console.log('拖拽结束，最终值:', event.value)

  try {
    // 跳转到指定位置
    const targetTime = event.value
    console.log('跳转到时间位置:', targetTime)

    // 调用seek方法跳转播放位置
    seek(targetTime)

    console.log('播放位置跳转完成')

    // 显示跳转提示
    uni.showToast({
      title: `跳转到 ${formatSecondsToMMSS(Math.floor(targetTime))}`,
      icon: 'none',
      duration: 1000,
    })
  }
  catch (error) {
    console.error('跳转播放位置失败:', error)
    uni.showToast({
      title: '跳转失败',
      icon: 'none',
    })
  }
  finally {
    // 重置拖拽状态
    isDragging.value = false
  }
}

/**
 * 添加change事件处理，用于调试
 */
function handleChange(event: { value: number }) {
  console.log('=== 滑块值改变 ===')
  console.log('change事件:', event)
  console.log('change值:', event.value)

  // 如果不是拖拽状态，说明是点击跳转
  if (!isDragging.value) {
    console.log('点击跳转到:', event.value)
    seek(event.value)
  }
}

function preventScroll(e: TouchEvent) {
  e.preventDefault()
}

// 监控displayProgress变化
watch(displayProgress, (newVal, oldVal) => {
  console.log('displayProgress变化:', { oldVal, newVal })
})

// 监控localProgress变化
watch(localProgress, (newVal, oldVal) => {
  console.log('localProgress变化:', { oldVal, newVal })
})

// 监控isDragging变化
watch(isDragging, (newVal, oldVal) => {
  console.log('isDragging变化:', { oldVal, newVal })
})

onMounted(() => {
  // 初始化localProgress
  localProgress.value = audioManagerMetaRef.value.currentTime
  console.log('组件挂载，初始化localProgress:', localProgress.value)
  console.log('当前音频总时长:', audioManagerMetaRef.value.totalDurtaion)

  if (sliderWrapper.value) {
    sliderWrapper.value.addEventListener('touchmove', preventScroll, { passive: false })
  }
})

onUnmounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.removeEventListener('touchmove', preventScroll)
  }
})
</script>

<template>
  <view ref="sliderWrapper" class="slider-wrapper no-scroll">
    <wd-slider
      :model-value="displayProgress"
      hide-label
      hide-min-max
      :max="audioManagerMetaRef.totalDurtaion"
      custom-class="audio-slider"
      active-color="#FFFFFF"
      inactive-color="#5f554b"
      @dragstart="handleDragStart"
      @dragmove="handleDragMove"
      @dragend="handleDragEnd"
      @change="handleChange"
    />
    <view class="time-wrapper">
      <view>{{ formatSecondsToMMSS(displayProgress) }}</view>
      <view>{{ audioManagerMetaRef.totalDurtaionLabel }}</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.no-scroll {
  touch-action: none;
  overflow: hidden;
}

.slider-wrapper {
  width: 590rpx;
  padding-left: 28rpx;
}

.audio-slider {
  --wot-slider-handle-bg: transparent;
  --wot-slider-axie-bg: transparent;
  --wot-slider-axie-height: 10rpx;

  :deep(.wd-slider__button) {
    opacity: 0 !important;
    box-shadow: none !important;
  }
}

.time-wrapper {
  // width: 590rpx;
  // padding-left: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--font-family-regular);
  font-weight: $font-weight-regular;
  font-size: $font-size-base;
  line-height: 18px;
  vertical-align: middle;
  position: relative;
  bottom: 10rpx;
}
</style>
