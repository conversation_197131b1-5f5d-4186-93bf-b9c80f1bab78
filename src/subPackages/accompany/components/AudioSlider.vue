<template>
  <view class="slider-wrapper no-scroll">
    <wd-slider v-model="value" hide-label hide-min-max custom-class="audio-slider" />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const value = ref(0)

function preventScroll(e: TouchEvent) {
  e.preventDefault()
}

onMounted(() => {
  const sliderEl = document.querySelector('.slider-wrapper')
  if (sliderEl) {
    sliderEl.addEventListener('touchmove', (e) => preventScroll(e as TouchEvent), { passive: false })
  }
})

onUnmounted(() => {
  const sliderEl = document.querySelector('.slider-wrapper')
  if (sliderEl) {
    sliderEl.removeEventListener('touchmove', (e) => preventScroll(e as TouchEvent))
  }
})
</script>

<style lang="scss">
::v-deep(.wd-slider__button) {
  box-shadow: none !important;
}

.no-scroll {
  touch-action: none;
  overflow: hidden;
}

.audio-slider {
  --wot-slider-handle-bg: transparent;
  --wot-slider-axie-bg: transparent;

  ::v-deep(.wd-slider__button) {
    box-shadow: none !important;
  }
}
</style>