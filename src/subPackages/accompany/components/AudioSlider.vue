<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useAudioStore } from '@/stores/audioManager'

const sliderWrapper = ref<HTMLElement | null>(null)
const audioStore = useAudioStore()
const {
  audioManagerMeta: audioManagerMetaRef,
} = storeToRefs(audioStore)

// 拖拽状态
const isDragging = ref(false)
// 锁定时间
const lockTime = ref(0)

// 监听当前时间
watch(
  () => audioManagerMetaRef.value.currentTime,
  (currentTime) => {
    if (!isDragging.value) {
      lockTime.value = currentTime
    }
  },
)

// 转换数据格式化
function formatSecondsToMMSS(totalSeconds: number): string {
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
}

function preventScroll(e: TouchEvent) {
  e.preventDefault()
}

onMounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.addEventListener('touchmove', preventScroll, { passive: false })
  }
})

onUnmounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.removeEventListener('touchmove', preventScroll)
  }
})

// 拖拽开始
function handleDragstart() {
  isDragging.value = true
}
// 拖拽结束
function handleDragend(val: any) {
  isDragging.value = false
  // 跳转到指定位置
  audioStore.seek(val.value)
}
</script>

<template>
  <view ref="sliderWrapper" class="slider-wrapper no-scroll">
    <wd-slider
      v-model="lockTime" hide-label hide-min-max :max="audioManagerMetaRef.totalDurtaion"
      custom-class="audio-slider" active-color="#FFFFFF" inactive-color="#5f554b" @dragend="(val) => handleDragend(val)"
      @dragstart="handleDragstart()"
    />
    <view class="time-wrapper">
      <view>{{ formatSecondsToMMSS(isDragging ? lockTime : audioManagerMetaRef.currentTime) }}</view>
      <view>{{ audioManagerMetaRef.totalDurtaionLabel }}</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.no-scroll {
  touch-action: none;
  overflow: hidden;
}

.slider-wrapper {
  width: 590rpx;
  padding-left: 28rpx;
}

.audio-slider {
  --wot-slider-handle-bg: transparent;
  --wot-slider-axie-bg: transparent;
  --wot-slider-axie-height: 10rpx;

  :deep(.wd-slider__button) {
    opacity: 0 !important;
    box-shadow: none !important;
  }
}

.time-wrapper {
  // width: 590rpx;
  // padding-left: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--font-family-regular);
  font-weight: $font-weight-regular;
  font-size: $font-size-base;
  line-height: 18px;
  vertical-align: middle;
  position: relative;
  bottom: 10rpx;
}
</style>
