<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { onMounted, onUnmounted, ref } from 'vue'
import { useAudioStore } from '@/stores/audioManager'

const sliderWrapper = ref<HTMLElement | null>(null)
const audioStore = useAudioStore()
const {
  audioManagerMeta: audioManagerMetaRef,
} = storeToRefs(audioStore)

// 转换数据格式化
function formatSecondsToMMSS(totalSeconds: number): string {
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`
}

function preventScroll(e: TouchEvent) {
  e.preventDefault()
}

onMounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.addEventListener('touchmove', preventScroll, { passive: false })
  }
})

onUnmounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.removeEventListener('touchmove', preventScroll)
  }
})
</script>

<template>
  <view ref="sliderWrapper" class="slider-wrapper no-scroll">
    <wd-slider
      v-model="audioManagerMetaRef.currentTime" hide-label hide-min-max
      :max="audioManagerMetaRef.totalDurtaion" custom-class="audio-slider" active-color="#FFFFFF"
      inactive-color="#5f554b"
    />
    <view class="time-wrapper">
      <view>{{ formatSecondsToMMSS(audioManagerMetaRef.currentTime) }}</view>
      <view>{{ audioManagerMetaRef.totalDurtaionLabel }}</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.no-scroll {
  touch-action: none;
  overflow: hidden;
}

.slider-wrapper {
  width: 590rpx;
  padding-left: 28rpx;
}

.audio-slider {
  --wot-slider-handle-bg: transparent;
  --wot-slider-axie-bg: transparent;
  --wot-slider-axie-height: 10rpx;

  :deep(.wd-slider__button) {
    opacity: 0 !important;
    box-shadow: none !important;
  }
}

.time-wrapper {
  // width: 590rpx;
  // padding-left: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family: var(--font-family-regular);
  font-weight: $font-weight-regular;
  font-size: $font-size-base;
  line-height: 18px;
  vertical-align: middle;
  position: relative;
  bottom: 10rpx;
}
</style>
