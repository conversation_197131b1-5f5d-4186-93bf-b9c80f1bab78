<script setup lang="ts">
const props = defineProps<{
  isLoading: boolean
  isLastPage: boolean
  list: any[]
}>()
</script>

<template>
  <!-- 加载更多提示 -->
  <view v-if="props.isLoading" class="loading-tip">
    <text>Loading...</text>
  </view>

  <!-- 没有更多数据提示 -->
  <view v-if="props.isLastPage && props.list.length > 0" class="no-more-tip">
    <text>No more data</text>
  </view>

  <!-- 空状态 -->
  <view v-if="!props.isLoading && list.length === 0" class="empty-state">
    <slot />
    <!-- <image src="/static/mine/Notification.png" class="empty-image" mode="aspectFit" />
    <text class="empty-title">
      Dialogue with History
    </text>
    <text class="empty-text">
      You need to create a new conversation, and the content after the conversation will be recorded here
    </text> -->
  </view>
</template>

<style scoped lang="scss">
.loading-tip {
  text-align: center;
  padding: 0 0 40rpx;
  color: #666;
  font-size: 28rpx;
}

.no-more-tip {
  text-align: center;
  padding: 0 0 40rpx;
  color: #999;
  font-size: 26rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 186rpx 0;
  width: 622rpx;
  text-align: center;
  margin: 0 auto;

}
</style>
