<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'

const props = defineProps<{
  currentIndex: number // 当前题库索引
  questionList: any[] // 题库列表
  type: string // 题库类型
  isShowTip: boolean // 是否展示错误提示
}>()
const emit = defineEmits<{
  (e: 'next'): void // 下一题
  (e: 'setAnswer', val: string): void // 设置答案
  (e: 'update:isShowTip', val: boolean): void // 更新错误提示
}>()

// 题库是否反转
const showBackArr = reactive<boolean[]>([])
// 是否答错
const isWrongArr = reactive<string[]>([])
onMounted(() => {
  if (props.type === 'mother-modal') {
    props.questionList.forEach(() => {
      showBackArr.push(false)
      isWrongArr.push('')
    })
  }
})

// 题目样式
function getQuestionClass(index: number) {
  if (index > props.currentIndex) {
    return 'followUp'
  }
  if (index < props.currentIndex) {
    return 'preposition'
  }
  if (index === props.questionList.length - 1) {
    return 'last'
  }
  return 'action'
}

// 题目标题
function stem() {
  if (props.type !== 'mother-modal') {
    return 'Question stem'
  }
  else {
    return 'MC'
  }
}

// 选中
function select(option: string, index: number) {
  // 已有错误选项
  if (isWrongArr[index]) {
    return
  }
  // 答错
  if (option !== props.questionList[index].criterion && props.type === 'mother-modal') {
    // 显示错误提示
    emit('update:isShowTip', true)
    setTimeout(() => {
      emit('update:isShowTip', false)
      emit('setAnswer', option)
      showBackArr[index] = true
    }, 2500)

    // 错误题库
    isWrongArr[index] = option
    return
  }
  emit('setAnswer', option)

  if (props.type !== 'mother-modal') {
    emit('next')
  }
  else {
    showBackArr[index] = true
  }
}

// 选择项处理
function handleOptionList() {
  // 人格测试题
  if (props.type === 'child-modal') {
    return [
      {
        value: '5',
        label: 'A. Very suitable',
      },
      {
        value: '4',
        label: 'B. Suitable',
      },
      {
        value: '3',
        label: 'C. General',
      },
      {
        value: '2',
        label: 'D. Inappropriate',
      },
      {
        value: '1',
        label: 'E. Very inappropriate',
      },
    ]
  }
  else {
    return [{
      value: '1',
      label: 'Disagree',
    }]
  }
}
</script>

<template>
  <view class="content">
    <view
      v-for="(item, index) in questionList" :key="item.id" class="question-box " :class="{
        'show-back': showBackArr[index],
        'actionLast': index === questionList.length - 1 && showBackArr[index],
        [getQuestionClass(index)]: true,

      }"
    >
      <view
        v-if="!showBackArr[index]" class="item" :class="{
          'wrong-box': isWrongArr[index],
          'boxShadow': index === currentIndex || index === questionList.length - 1,
        }"
      >
        <text class="stem">
          {{ stem() }}
        </text>

        <!-- 题目 -->
        <view class="title">
          {{ item.title }}
        </view>

        <!-- 选项 -->
        <view
          v-for="(option) in handleOptionList()" :key="option.value" class="option"
          :class="{ active: item.correctAnswer === option.value, wrong: isWrongArr[index] === option.value }"
          @click="select(option.value, index)"
        >
          <view class="divide" />{{ option.label }}
        </view>
      </view>

      <!-- 卡片背面 -->
      <view
        v-else class="item back-content" :class="{
          [getQuestionClass(index)]: true,
          'wrong-box': isWrongArr[index],
          'boxShadow': index === currentIndex,
        }"
      >
        <text class="stem">
          Encyclopedia popular science
        </text>

        <!-- 科学解释内容 -->
        <view class="science-content">
          {{ item.science }}
        </view>
      </view>
    </view>
    <wd-transition :show="isShowTip" name="fade" :duration="100">
      <view class="tip">
        <image src="/static/accompany/wrong.png" mode="scaleToFill" />
      </view>
    </wd-transition>
  </view>
</template>

<style scoped lang="scss">
.content {
  width: 686rpx;
  flex: 1;
  margin-top: 40rpx;
  position: relative;
  display: flex;
  align-items: center;

  .show-back {
    transform: rotate3d(0, 1, 0, 180deg);
  }

  .question-box {
    position: absolute;
    height: 100%;
    transition: all 0.6s ease-in-out;

    .boxShadow {
      box-shadow: 6px 6px 24px 0px #E7670026;
    }

    .item {
      width: 640rpx;
      height: 100%;
      overflow-y: auto;
      border-radius: 40rpx;
      border: 1px solid #FFCAA1;
      background-color: #fff;
      padding: 40rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 32rpx;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
      }

      scrollbar-width: none;
      /* Firefox */
      -ms-overflow-style: none;
      /* IE 10+ */

      .stem {
        padding: 0 20rpx;
        height: 46rpx;
        background-color: $color-primary;
        color: #fff;
        border-radius: 10rpx;
        line-height: 46rpx;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        font-family: var(--font-family-regular);
        text-align: center;
        white-space: nowrap;
        max-width: max-content;
      }

      .title {
        font-family: var(--font-family-regular);
        font-weight: $font-weight-regular;
        font-size: $font-size-lg;
        line-height: 150%;
        letter-spacing: 2%;
      }

      .option {
        width: 100%;
        height: 80rpx;
        border-radius: 40rpx;
        background-color: #f9fafb;
        border: 1px solid #f9fafb;
        font-family: var(--font-family-regular);
        font-weight: $font-weight-regular;
        font-size: $font-size-lg;
        letter-spacing: 2%;
        line-height: 80rpx;
        vertical-align: middle;
        padding: 0 20rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        color: #1d232e;

        .divide {
          width: 8rpx;
          height: 8rpx;
          background-color: #1d232e;
          border-radius: 50%;
          margin: 0 16rpx;
        }
      }

      .active {
        border-color: $color-primary;
        color: $color-primary;

        .divide {
          background-color: $color-primary;
        }
      }

      .wrong {
        border-color: $auxiliary-red-light;
        background-color: $auxiliary-red-light;
      }

      .science-content {
        flex: 1;
        font-family: var(--font-family-regular);
        font-weight: $font-weight-regular;
        font-size: $font-size-md;
        color: #1d232e;
        padding-top: 8rpx;
      }

      // 背面内容反转，让文字正常显示
      &.back-content {
        transform: rotate3d(0, 1, 0, 180deg);
      }
    }

    .wrong-box {
      border-color: $auxiliary-red-light;
      background-color: #FFF0F0;
    }

  }

  .action {
    z-index: 10;
  }

  .followUp {
    transform: translateX(46rpx);
    z-index: 5;
    height: calc(100% - 80rpx);
    box-sizing: border-box;
  }

  .preposition {
    z-index: 15;
    transform: translateX(-150%);
  }

  .last {
    transform: translateX(23rpx);
  }

  .actionLast {
    z-index: 1;
    transform: rotate3d(0, 1, 0, 180deg) translateX(-23rpx);
  }
}

.tip {
  position: absolute;
  z-index: 1000;
  bottom: 160rpx;
  left: 122rpx;
  width: 442rpx;
  height: 128rpx;

  image {
    width: 100%;
    height: 100%;
  }
}
</style>
