<script setup lang="ts">
import { ref } from 'vue'

const isFloating = ref(false)

function handleClick() {
  isFloating.value = true

  // 动画结束后移除类名，以便下次再次触发
  setTimeout(() => {
    isFloating.value = false

    uni.navigateTo({
      url: '/subPackages/accompany/bedtimeStory',
    })
  }, 600) // 与动画持续时间一致
}
</script>

<template>
  <view class="moon" :class="{ floating: isFloating }" @click="handleClick">
    <image src="/static/accompany/moon.png" mode="scaleToFill" />
  </view>
</template>

<style scoped lang="scss">
.moon {
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  top: -12rpx;
  right: -6rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

/* 定义点击后浮动动画 */
.moon.floating {
  animation: float 0.6s ease-in-out;
}

@keyframes float {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0);
  }
}
</style>
