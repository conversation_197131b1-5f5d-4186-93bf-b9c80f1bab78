<script setup lang="ts">
const props = defineProps<{
  title?: string
  content?: string
  subtitle?: string
}>()

// 高亮部分
const highlightedText = (props.content && props.subtitle)
  ? props.content.replace(
      new RegExp(props.subtitle || '', 'g'),
      `<span style="font-family: var(--font-family-title); font-weight:700;">${props.subtitle}</span>`,
    )
  : props.content
</script>

<template>
  <view class="avatar-container shadow">
    <!-- 标题 -->
    <view v-if="props.title" class="title-wrap">
      <view class="title-text">
        {{ props.title }}
      </view>
    </view>

    <!-- 内容 -->
    <div v-if="props.content" class="content" v-html="highlightedText" />
    <template v-else>
      <slot />
    </template>
  </view>
</template>

<style scoped lang="scss">
.shadow {
  border: 1px solid #FFCAA1;
  box-shadow: 6px 6px 24px 0px #F0914426;
  padding: 40rpx;
  box-sizing: border-box;
  width: 686rpx;
  border-radius: 40rpx;
  margin: 0 auto;
}

.avatar-container {
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  align-items: center;

  .title-wrap {
    padding: 0 40rpx;
    height: 78rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 40rpx;

    border: 2px solid #FF9844;

    background: linear-gradient(265.6deg, rgba(255, 153, 62, 0.2) -4.76%, rgba(255, 146, 89, 0.2) 53.62%, rgba(255, 159, 118, 0.2) 96.43%);

    .title-text {
      font-size: 32rpx;
      font-weight: bold;

      /* 渐变文字 */
      background: linear-gradient(92.74deg, #FF9037 9.43%, #F86F00 96.54%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      /* 兼容部分 App 端 Android */
      background-clip: text;
      color: transparent;
    }
  }
}

.content {
  font-family: var(--font-family-light);
  font-weight: $font-weight-light;
  font-size: $font-size-md;
  line-height: 158%;
  text-align: justify;
  vertical-align: middle;

}
</style>
