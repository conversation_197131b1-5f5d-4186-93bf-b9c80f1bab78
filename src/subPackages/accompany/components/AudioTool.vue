<template>
  <view class="tool-container">
    <view class="tool-item" @click="reloadAudio">
      <image src="/static/accompany/reload.png" mode="scaleToFill" />
    </view>
    <view class="tool-item" @clcick="backAudio">
      <image src="/static/accompany/back.png" mode="scaleToFill" />
    </view>
    <view class="tool-start" @click="playAudio">
      <image src="/static/accompany/play.png" mode="scaleToFill" />
    </view>
    <view class="tool-item" @click="forwardAudio">
      <image src="/static/accompany/forward.png" mode="scaleToFill" />
    </view>
    <view class="tool-item">
      <image src="/static/accompany/random.png" mode="scaleToFill" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { useAudioStore } from '@/stores/audioManager';

const audioStore = useAudioStore()
const {
  play,

} = audioStore

// 播放
function playAudio() {
  play()
}

// 上一首
function backAudio() {
}

// 下一首
function forwardAudio() {
}

// 重播
function reloadAudio() {
}
</script>

<style scoped lang="scss">
.tool-container {
  width: 590rpx;
  padding-left: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 44rpx;

  .tool-item {
    width: 48rpx;
    height: 48rpx;
  }

  .tool-start {
    width: 104rpx;
    height: 104rpx;
  }

  image {
    width: 100%;
    height: 100%;
  }
}
</style>