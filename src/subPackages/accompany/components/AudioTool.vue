<script setup lang="ts">
import { computed } from 'vue'
import { useAudioStore } from '@/stores/audioManager'

const audioStore = useAudioStore()
const {
  play,
  pause,
  seek,
  audioManagerMeta,
  getAudioManagerPlayState,
  setAudioManagerInfo,
} = audioStore

/**
 * 播放/暂停切换
 * 根据当前播放状态决定播放或暂停
 */
function playAudio() {
  try {
    console.log('=== playAudio 被调用 ===')
    console.log('当前音频元数据:', JSON.stringify(audioManagerMeta, null, 2))

    // 检查音频源是否存在
    if (!audioManagerMeta.src) {
      console.error('音频源为空，无法播放')
      uni.showToast({
        title: '音频源未设置',
        icon: 'none',
      })
      return
    }

    const isPlaying = getAudioManagerPlayState()
    console.log('当前播放状态:', isPlaying)

    if (isPlaying) {
      console.log('执行暂停操作...')
      pause()
      console.log('暂停播放完成')
      uni.showToast({
        title: '暂停播放',
        icon: 'none',
        duration: 1000,
      })
    }
    else {
      console.log('执行播放操作...')
      // 确保音频管理器信息已设置
      console.log('设置音频管理器信息...')
      setAudioManagerInfo()
      console.log('调用play方法...')
      play()
      console.log('开始播放完成')
      uni.showToast({
        title: '开始播放',
        icon: 'none',
        duration: 1000,
      })
    }
  }
  catch (error) {
    console.error('播放控制失败:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    uni.showToast({
      title: `播放控制失败: ${errorMessage}`,
      icon: 'none',
    })
  }
}

/**
 * 重播功能 - 从头开始播放
 * 将播放位置重置到0秒并开始播放
 */
function reloadAudio() {
  try {
    console.log('reloadAudio 被调用')

    // 检查音频源是否存在
    if (!audioManagerMeta.src) {
      console.error('音频源为空，无法重播')
      uni.showToast({
        title: '音频源未设置',
        icon: 'none',
      })
      return
    }

    // 确保音频管理器信息已设置
    setAudioManagerInfo()
    // 跳转到开始位置
    seek(0)
    // 开始播放
    play()
    console.log('重新播放')

    uni.showToast({
      title: '重新播放',
      icon: 'none',
      duration: 1000,
    })
  }
  catch (error) {
    console.error('重播失败:', error)
    uni.showToast({
      title: '重播失败',
      icon: 'none',
    })
  }
}

/**
 * 计算播放按钮显示的图标
 * 根据当前播放状态返回对应的图标路径
 */
const playButtonIcon = computed(() => {
  const isPlaying = audioManagerMeta.playState
  return isPlaying ? '/static/accompany/pause.png' : '/static/accompany/play.png'
})

/**
 * 计算播放按钮的标题提示
 */
const playButtonTitle = computed(() => {
  const isPlaying = audioManagerMeta.playState
  return isPlaying ? '暂停' : '播放'
})
</script>

<template>
  <view class="tool-container">
    <!-- 重播按钮 -->
    <view class="tool-item" title="重新播放" @click="reloadAudio">
      <image src="/static/accompany/reload.png" mode="scaleToFill" />
    </view>

    <!-- 上一首 -->
    <view class="tool-item">
      <image src="/static/accompany/back.png" mode="scaleToFill" />
    </view>

    <!-- 播放/暂停按钮 -->
    <view class="tool-start" :title="playButtonTitle" @click="playAudio">
      <image :src="playButtonIcon" mode="scaleToFill" />
    </view>

    <!-- 下一首 -->
    <view class="tool-item">
      <image src="/static/accompany/forward.png" mode="scaleToFill" />
    </view>

    <!-- 随机播放按钮 -->
    <view class="tool-item" title="随机播放">
      <image src="/static/accompany/random.png" mode="scaleToFill" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.tool-container {
  width: 590rpx;
  padding-left: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 44rpx;

  .tool-item {
    width: 48rpx;
    height: 48rpx;
  }

  .tool-start {
    width: 104rpx;
    height: 104rpx;
  }

  image {
    width: 100%;
    height: 100%;
  }
}
</style>
