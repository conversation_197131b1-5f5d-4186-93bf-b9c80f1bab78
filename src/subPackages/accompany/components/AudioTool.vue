<template>
  <view class="tool-container">
    <!-- 重播按钮 -->
    <view class="tool-item" @click="reloadAudio" :title="'重新播放'">
      <image src="/static/accompany/reload.png" mode="scaleToFill" />
    </view>

    <!-- 上一首 -->
    <view class="tool-item" @click="">
      <image src="/static/accompany/back.png" mode="scaleToFill" />
    </view>

    <!-- 播放/暂停按钮 -->
    <view class="tool-start" @click="playAudio" :title="playButtonTitle">
      <image :src="playButtonIcon" mode="scaleToFill" />
    </view>

    <!-- 下一首 -->
    <view class="tool-item" @click="">
      <image src="/static/accompany/forward.png" mode="scaleToFill" />
    </view>

    <!-- 随机播放按钮 -->
    <view class="tool-item" :title="'随机播放'">
      <image src="/static/accompany/random.png" mode="scaleToFill" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAudioStore } from '@/stores/audioManager'

const audioStore = useAudioStore()
const {
  play,
  pause,
  seek,
  forward,
  backward,
  audioManagerMeta,
  getAudioManagerPlayState
} = audioStore

/**
 * 播放/暂停切换
 * 根据当前播放状态决定播放或暂停
 */
function playAudio() {
  try {
    const isPlaying = getAudioManagerPlayState()
    if (isPlaying) {
      pause()
      console.log('暂停播放')
    } else {
      play()
      console.log('开始播放')
    }
  } catch (error) {
    console.error('播放控制失败:', error)
    uni.showToast({
      title: '播放控制失败',
      icon: 'none'
    })
  }
}

/**
 * 重播功能 - 从头开始播放
 * 将播放位置重置到0秒并开始播放
 */
function reloadAudio() {
  try {
    // 跳转到开始位置
    seek(0)
    // 开始播放
    play()
    console.log('重新播放')

    uni.showToast({
      title: '重新播放',
      icon: 'none',
      duration: 1000
    })
  } catch (error) {
    console.error('重播失败:', error)
    uni.showToast({
      title: '重播失败',
      icon: 'none'
    })
  }
}

/**
 * 计算播放按钮显示的图标
 * 根据当前播放状态返回对应的图标路径
 */
const playButtonIcon = computed(() => {
  const isPlaying = audioManagerMeta.playState
  return isPlaying ? '/static/accompany/pause.png' : '/static/accompany/play.png'
})

/**
 * 计算播放按钮的标题提示
 */
const playButtonTitle = computed(() => {
  const isPlaying = audioManagerMeta.playState
  return isPlaying ? '暂停' : '播放'
})
</script>

<style scoped lang="scss">
.tool-container {
  width: 590rpx;
  padding-left: 28rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 44rpx;

  .tool-item {
    width: 48rpx;
    height: 48rpx;
  }

  .tool-start {
    width: 104rpx;
    height: 104rpx;
  }

  image {
    width: 100%;
    height: 100%;
  }
}
</style>