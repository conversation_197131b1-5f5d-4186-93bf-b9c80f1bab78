<script setup lang="ts">
const props = defineProps<{
  item: any
}>()
</script>

<template>
  <view class="item">
    <view class="time">
      {{ props.item.update_time }}<wd-icon name="arrow-right" size="18px" color="#1D232E" />
    </view>
    <view class="content">
      <image src="/static/mine/report.png" mode="scaleToFill" />
      <view class="info">
        <view class="info-title">
          {{ props.item.name_en }}
        </view>
        <view class="info-subtitle">
          {{ props.item.subtitle }}
        </view>
        <view class="info-content">
          {{ props.item.content }}
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.item {
  width: 686rpx;
  // height: 428rpx;
  border: 1px solid #FFCAA1;
  border-radius: 40rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
  box-shadow: 6px 6px 24px 0px #********;
  padding: 40rpx;
  box-sizing: border-box;

  .time {
    height: 40rpx;
    line-height: 40rpx;
    padding-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #EAECF0;
    font-family: var(--font-family-light);
    font-weight: $font-weight-light;
    font-size: $font-size-base;
    letter-spacing: 2%;
    vertical-align: middle;
    color: #667085;
  }

  .content {
    display: flex;
    align-items: center;
    gap: 40rpx;
    padding-top: 20rpx;

    image {
      width: 180rpx;
      height: 180rpx;
      background-color: aqua;
    }

    .info {
      min-height: 268rpx;
      width: 386rpx;

      .info-title {
        height: 38rpx;
        line-height: 38rpx;
        font-family: var(--font-family-title);
        font-weight: $font-weight-bold;
        font-size: $font-size-lg;
        letter-spacing: 2rpx;
        vertical-align: middle;

        background: $gradient-color-three-element;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        /* 替代 text-shadow */
        filter: drop-shadow(2px 2px rgba(48, 57, 117, 0.3));
      }

      .info-subtitle {
        margin-top: 12rpx;
        // height: 38rpx;
        line-height: 38rpx;
        font-weight: $font-weight-regular;
        font-size: $font-size-lg;
        letter-spacing: 2%;
        vertical-align: middle;
        color: #1D232E;
      }

      .info-content {
        margin-top: 20rpx;
        font-weight: $font-weight-regular;
        font-size: $font-size-base;
        line-height: 130%;
        letter-spacing: 2%;
        vertical-align: middle;
        color: #667085;
      }
    }
  }
}
</style>
