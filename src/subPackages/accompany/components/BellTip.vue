<script setup lang="ts">
import { ref } from 'vue'
import StoryModal from './StoryModal.vue'

const props = defineProps<{
  type: 'child-modal' | 'mother-modal' | 'father-modal'
}>()

// 定义事件
const emit = defineEmits<{
  close: []
}>()

// 控制显示/隐藏
const visible = ref(false)

// 显示提示
function show() {
  visible.value = true
}

// 隐藏提示
function hide() {
  visible.value = false
  emit('close')
}

// 点击遮罩层关闭
function handleOverlayClick() {
  hide()
}

// report事件
function handleReport() {
  uni.navigateTo({
    url: `/subPackages/accompany/reportList?type=${props.type}`,
  })
}

// 暴露方法给父组件调用
defineExpose({
  show,
  hide,
})
</script>

<template>
  <view
    v-if="visible" class="bell-tip-overlay" :class="{ 'is-modal': type.includes('modal') }"
    :style="{ padding: type === 'mother-modal' ? '180rpx 76rpx 108rpx' : '88rpx 76rpx 80rpx' }"
    @click="handleOverlayClick"
  >
    <StoryModal v-if="type.includes('modal')" :type="type" @click.stop @report="handleReport" @hide="hide" />
  </view>
</template>

<style scoped lang="scss">
.bell-tip-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 88rpx 76rpx 80rpx;
  box-sizing: border-box;

  // 毛玻璃背景效果
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);

  // 兼容性处理，如果不支持backdrop-filter则使用半透明背景
  @supports not (backdrop-filter: blur(8px)) {
    background: rgba(0, 0, 0, 0.5);
  }
}

.is-modal {
  z-index: 9999;
  // 毛玻璃背景效果
  background: rgba(22, 22, 22, 0.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}
</style>
