<template>
  <view class="slider-wrapper no-scroll" ref="sliderWrapper">
    <view class="tool-item">
      <image src="/static/accompany/volumeDown.png" mode="scaleToFill" />
    </view>
    <wd-slider v-model="value" hide-label hide-min-max :max="100" custom-class="voice-slider" active-color="#FFFFFF"
      inactive-color="#5f554b" @dragmove="(val) => handleDragmove(val)" />
    <view class="tool-item">
      <image src="/static/accompany/volume.png" mode="scaleToFill" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

const sliderWrapper = ref<HTMLElement | null>(null)

// 获取系统默认音量
const value = ref(plus.device.getVolume() * 100)

// 调节系统音量
function handleDragmove(val: any) {
  const volume = Math.max(0, Math.min(1, Number(val.value) / 100))

  plus.device.setVolume(volume)
}

function preventScroll(e: TouchEvent) {
  e.preventDefault()
}

onMounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.addEventListener('touchmove', preventScroll, { passive: false })
  }
})

onUnmounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.removeEventListener('touchmove', preventScroll)
  }
})
</script>

<style lang="scss" scoped>
.no-scroll {
  touch-action: none;
  overflow: hidden;
}

.slider-wrapper {
  margin-top: 44rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.voice-slider {
  width: 514rpx;
  justify-content: space-between;
  --wot-slider-handle-bg: transparent;
  --wot-slider-axie-bg: transparent;

  :deep(.wd-slider__button) {
    opacity: 0 !important;
    box-shadow: none !important;
  }
}

.tool-item {
  width: 48rpx;
  height: 48rpx;

  image {
    width: 100%;
    height: 100%;
  }
}
</style>
