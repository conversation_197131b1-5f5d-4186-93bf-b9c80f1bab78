<template>
  <view class="slider-wrapper no-scroll" ref="sliderWrapper">
    <!-- 音量减小按钮 -->
    <view class="tool-item" @click="decreaseVolume" title="音量减小">
      <image src="/static/accompany/volumeDown.png" mode="scaleToFill" />
    </view>

    <!-- 音量滑块 -->
    <wd-slider v-model="value" hide-label hide-min-max :max="100" custom-class="voice-slider" active-color="#FFFFFF"
      inactive-color="#5f554b" />

    <!-- 音量增大按钮 -->
    <view class="tool-item" @click="increaseVolume" title="音量增大">
      <image src="/static/accompany/volume.png" mode="scaleToFill" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'

const sliderWrapper = ref<HTMLElement | null>(null)


const value = ref(0)
/**
 * 获取系统默认音量
 * 在App环境下使用plus.device.getVolume()，其他环境使用默认值
 */
function getInitialVolume(): number {
  // #ifdef APP-PLUS
  try {
    value.value = plus.device.getVolume() * 100
  } catch (error) {
    console.warn('获取系统音量失败:', error)
    value.value = 50 // 默认音量50%
  }
  // #endif

  // #ifndef APP-PLUS
  return 50 // 非App环境默认音量50%
  // #endif
}
getInitialVolume()

/**
 * 设置系统音量
 * @param volumePercent 音量百分比 (0-100)
 */
function setSystemVolume(volumePercent: number) {
  const volume = Math.max(0, Math.min(1, volumePercent / 100))

  // #ifdef APP-PLUS
  try {
    plus.device.setVolume(volume)
    console.log(`设置系统音量: ${volumePercent}%`)
  } catch (error) {
    console.error('设置系统音量失败:', error)
    uni.showToast({
      title: '音量调节失败',
      icon: 'none'
    })
  }
  // #endif

  // #ifndef APP-PLUS
  console.log(`模拟设置音量: ${volumePercent}%`)
  // #endif
}

watch(value, (newVal) => {
  setSystemVolume(newVal)
})

/**
 * 音量减小功能
 * 每次点击减少10%音量
 */
function decreaseVolume() {
  try {
    const newVolume = Math.max(0, value.value - 10)
    value.value = newVolume
    setSystemVolume(newVolume)
  } catch (error) {
    console.error('音量减小失败:', error)
  }
}

/**
 * 音量增大功能
 * 每次点击增加10%音量
 */
function increaseVolume() {
  try {
    const newVolume = Math.min(100, value.value + 10)
    value.value = newVolume
    setSystemVolume(newVolume)
  } catch (error) {
    console.error('音量增大失败:', error)
  }
}

/**
 * 防止滚动事件处理
 * @param e 触摸事件
 */
function preventScroll(e: TouchEvent) {
  e.preventDefault()
}

onMounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.addEventListener('touchmove', preventScroll, { passive: false })
  }
})

onUnmounted(() => {
  if (sliderWrapper.value) {
    sliderWrapper.value.removeEventListener('touchmove', preventScroll)
  }
})
</script>

<style lang="scss" scoped>
.no-scroll {
  touch-action: none;
  overflow: hidden;
}

.slider-wrapper {
  margin-top: 44rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.voice-slider {
  width: 514rpx;
  justify-content: space-between;
  --wot-slider-handle-bg: transparent;
  --wot-slider-axie-bg: transparent;

  :deep(.wd-slider__button) {
    opacity: 0 !important;
    box-shadow: none !important;
  }
}

.tool-item {
  width: 48rpx;
  height: 48rpx;

  image {
    width: 100%;
    height: 100%;
  }
}
</style>
