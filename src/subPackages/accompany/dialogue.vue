<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'

// 输入框
const inputValue = ref('')
const bottomAnchorId = 'chat-bottom'
const scrollToId = ref('')

const chatList = ref<{ role: string, content: string, id: number, time: number }[]>([
  {
    role: 'user',
    content: 'I have problems with my hands, which have been hurting lately.',
    id: 1,
    time: new Date().getTime(),
  },
  {
    role: 'chat',
    content: 'Can you tell me the problem you are having? So that I can Idenfity it.Can you tell me the problem you are having? So that I can Idenfity it.Can you tell me the problem you are having? So that I can Idenfity it.Can you tell me the problem you are having? So that I can Idenfity it.Can you tell me the problem you are having? So that I can Idenfity it.Can you tell me the problem you are having? So that I can Idenfity it.Can you tell me the problem you are having? So that I can Idenfity it.Can you tell me the problem you are having? So that I can Idenfity it.Can you tell me the problem you are having? So that I can Idenfity it.Can you tell me the problem you are having? So that I can Idenfity it.',
    id: 2,
    time: new Date().getTime(),
  },
  {
    role: 'user',
    content: 'I have problems with my hands, which have been hurting lately.',
    id: 3,
    time: new Date().getTime(),
  },
])

// 滚动至最底部锚点
function scrollToBottom() {
  scrollToId.value = ''
  nextTick(() => {
    scrollToId.value = bottomAnchorId
  })
}

// 组件挂载时滚动至最底部
onMounted(() => {
  scrollToBottom()
})
// scrollToBottom();

// 模拟字符逐个显示的假流式数据效果
function simulateStreamingResponse(text: string, callback: (chunk: string) => void, done: () => void) {
  let index = 0
  const interval = 30 // 每个字出现的时间
  const step = () => {
    if (index < text.length) {
      callback(text[index])
      scrollToBottom()
      index++
      setTimeout(step, interval)
    }
    else {
      done()
    }
  }
  step()
}

// 处理用户发送消息逻辑及流式模拟回复
function handleSeed(val: string) {
  if (val.trim() === '')
    return
  console.log('提交', val)
  inputValue.value = ''
  const userMessage = {
    role: 'user',
    content: val,
    id: chatList.value.length + 1,
    time: new Date().getTime(),
  }
  chatList.value.push(userMessage)
  scrollToBottom()

  const replyId = userMessage.id + 1
  const replyMessage = {
    role: 'chat',
    content: '',
    id: replyId,
    time: new Date().getTime(),
  }
  chatList.value.push(replyMessage)
  scrollToBottom()

  const fullReply = 'Thank you for sharing. Can you describe the pain in more detail?'

  simulateStreamingResponse(
    fullReply,
    (chunk) => {
      const reply = chatList.value.find(item => item.id === replyId)
      if (reply)
        reply.content += chunk
    },
    () => {
      console.log('回复结束')
    },
  )
}

// 新会话
function newSession() {
  chatList.value = []
}
// 会话列表
function sessionList() {
  uni.navigateTo({
    url: '/subPackages/accompany/dialogueList',
  })
}
</script>

<template>
  <view class="page-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          New Dialogue
        </view>
      </template>
      <template #right>
        <view class="right-slot">
          <wd-icon v-if="chatList.length > 0" name="add" size="40rpx" color="#667085" @click="newSession" />
          <wd-icon v-else name="view-list" size="48rpx" color="#667085" @click="sessionList" />
        </view>
      </template>
    </TopNav>

    <view class="content">
      <Empty v-if="chatList.length === 0" v-model:model-value="inputValue" @enter="(val) => handleSeed(val)" />
      <scroll-view v-else :show-scrollbar="false" scroll-y class="chat-list" :scroll-into-view="scrollToId">
        <DialogBox v-for="item in chatList" :key="item.id" :item="item" :data="item" />
        <view :id="bottomAnchorId" style="height: 1px;" />
      </scroll-view>
    </view>

    <view class="input-wrapper-spacer" />

    <view class="input-wrapper">
      <InputComponent v-model:value="inputValue" @enter="(val) => handleSeed(val)" />
      <view class="safe-area-placeholder" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .right-slot {
    display: flex;
    justify-content: end;
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    overflow: auto;

    .chat-list {
      width: 686rpx;
      position: relative;
      height: 100%;
      padding-top: 40rpx;
      overflow: auto;
    }
  }
}

.input-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  // padding: 16rpx;
  display: flex;
  justify-content: center;
  background-color: #fff;
  z-index: 999;
}

.safe-area-placeholder {
  height: env(safe-area-inset-bottom);
}

.input-wrapper-spacer {
  height: 120rpx;
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
