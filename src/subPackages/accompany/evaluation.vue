<script setup lang="ts">
import { onBackPress, onLoad } from '@dcloudio/uni-app'
import { computed, ref } from 'vue'
import { reportApi } from '@/api/accompany.js'
import QuestionBox from './components/QuestionBox.vue'
import ProgressBar from './components/ProgressBar.vue'

const type = ref<string>('')

onLoad((options) => {
  if (options?.type) {
    type.value = options.type
    getQuestionList(options.type)
  }
})
// 退出
const showModal = ref(false)

// 题目列表
const questionList = ref<any>([])
// 当前题目索引
const currentIndex = ref(0)

// 展示错误提示
const isShowTip = ref(false)

// 已有答案的索引
const answerIndex = computed(() => {
  return questionList.value.findLastIndex((item: any) => item.correctAnswer)
})
// 已有答案数量
const answerCount = computed(() => {
  return questionList.value.filter((item: any) => item.correctAnswer).length
})

// 获取题目列表
async function getQuestionList(type: string) {
  uni.showLoading({
    title: 'Loading',
  })
  try {
    if (type === 'child-modal') {
      const res = await reportApi.getTopics({
        baby_id: 4
      })
      questionList.value = res
    }

    uni.hideLoading()
  } catch (error) {
    console.log(error)
  }
}

// 设置答案
function setAnswer(val: string) {
  // 百科答题无法更改答案
  if (type.value === 'mother-modal' && questionList.value[currentIndex.value].correctAnswer) return

  questionList.value[currentIndex.value].correctAnswer = val

}
// 下一题
async function next() {
  if (currentIndex.value < questionList.value.length - 1) {
    currentIndex.value++
  } else {
    try {
      uni.showToast({
        title: 'It\'s already the last question',
        icon: 'none',
      })
      const answerList = questionList.value.map((item: any) => {
        return {
          "id": item.id,
          "pid": item.pid,
          "answer": item.correctAnswer
        }
      })
      await reportApi.answer({
        baby_id: 4,
        answer_questions: answerList
      })
      uni.redirectTo({
        url: `/subPackages/accompany/reportDetail?type=${type.value}&id=${1}`,
      })
    } catch (e) {
      console.log(e)
    }
  }
}

// 监听返回键
onBackPress(({ from }) => {
  if (from === 'backbutton') {
    // 可以在这里执行一些清理或确认逻辑
    console.log('返回按钮被拦截')

    // 返回 true 阻止默认返回行为
    return true
  }
})
// 返回按钮
function goBack() {
  // 错误状态禁用
  if (isShowTip.value) {
    return
  }

  if (currentIndex.value > 0) {
    currentIndex.value--
  }
  else {
    showModal.value = true
  }
}
// 确认返回
function handleConfirm() {
  uni.navigateBack()
}
</script>

<template>
  <view class="page-container">
    <TopNav>
      <template #left>
        <wd-icon name="thin-arrow-left" size="14px" @click="showModal = true" />
      </template>
      <template #middle>
        <view class="middle-slot">
          Children Evaluation
        </view>
      </template>
      <!-- <template #right>
        <view class="right-slot">
          Report
        </view>
      </template> -->
    </TopNav>

    <view class="container">
      <!-- 进度条 -->
      <ProgressBar :answer-count="answerCount" :question-count="questionList.length" />

      <!-- 题目卡片 -->
      <QuestionBox :current-index="currentIndex" :question-list="questionList" @setAnswer="(val) => setAnswer(val)"
        @next="next" :type="type" v-model:isShowTip="isShowTip" />

      <view class="backBtn">
        <view class="back" @click="goBack">
          <image src="/static/accompany/ArrowLeft.png" mode="scaleToFill" />
        </view>
        <view v-if="currentIndex <= answerIndex" class="back" @click="next">
          <image src="/static/accompany/Check.png" mode="scaleToFill" />
        </view>
      </view>
    </view>
    <Modal :visible="showModal" title="Are you really going to leave?"
      content="Exit will result in the loss of thefilled-in answers. You can startthe test again at any time."
      cancel-text="Quit" confirm-text="Continue" @update:visible="showModal = $event" @cancel="handleConfirm"
      @confirm="showModal = false">
      <view class="modal-box" />
    </Modal>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-title);
  display: flex;
  flex-direction: column;

  .right-slot {
    display: flex;
    justify-content: end;
    font-size: $font-size-md;
    color: #98a2b3;
  }

  .container {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .backBtn {
      width: 100%;
      padding: 0 75rpx;
      box-sizing: border-box;
      height: 180rpx;
      background-color: #fff;
      margin-top: 18rpx;
      border-radius: 40rpx 40rpx 0 0;
      box-shadow: 0px -8px 24px 0px #FDA05526;
      padding-bottom: env(safe-area-inset-bottom, 0px);
      display: flex;
      justify-content: center;
      gap: 40rpx;

      .back {
        flex: 1;
        margin: 40rpx auto;
        height: 100rpx;
        background: $gradient-color-light;
        border-radius: 100rpx;
        box-shadow: 0px 8px 16px 0px #FDA05559;
        display: flex;
        justify-content: center;
        align-items: center;

        image {
          width: 60rpx;
          height: 60rpx;
        }

      }
    }
  }

  .modal-box {
    width: 502rpx;
    height: 670rpx;
    margin: 0 auto;
    background-color: #d0d5dd;
  }
}
</style>
