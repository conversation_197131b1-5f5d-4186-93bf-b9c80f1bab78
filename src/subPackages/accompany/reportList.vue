<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { ref } from 'vue'
import { reportApi } from '@/api/accompany.js'
import ScrollEmpty from './components/ScrollEmpty.vue'
import ReportItem from './components/ReportItem.vue'

const type = ref<string>('')

onLoad((options) => {
  if (options?.type) {
    type.value = options.type
  }
})

const reportList = ref([{
  id: 1,
  time: '2023-09-01 09:00:00',
  title: 'Little Comforter',
  subtitle: 'Logistics specialist',
  content: 'Pragmatic and rigorous, paying attention to details, having a strong sense of responsibility, and being good at process management and execution.',
}])
const parData = ref(
  {
    page: 1,
    isLastPage: false,
    isLoading: false,
    isRefreshing: false,
  },
)
// 上拉分页加载
async function handleScrolltolower() {
  const currentTab = parData.value
  if (currentTab.isLastPage || currentTab.isLoading) {
    return
  }
  currentTab.page++
  await getReportList()
}

// 下拉刷新
async function handleRefresh() {
  const currentTab = parData.value
  currentTab.isRefreshing = true
  currentTab.page = 1
  currentTab.isLastPage = false
  await getReportList(true)
}

// 获取列表
async function getReportList(isRefresh = false) {
  const currentTab = parData.value
  if (currentTab.isLoading)
    return

  currentTab.isLoading = true

  try {
    console.log('getReportList', {
      page: currentTab.page,
      isRefresh,
    })

    const mockData: any = await reportApi.reportList({
      page_no: currentTab.page,
      page_size: 10
    })

    // 是否最后一页
    if (currentTab.page * 10 >= mockData.count) {
      currentTab.isLastPage = true
    }

    if (isRefresh || currentTab.page === 1) {
      reportList.value = mockData.lists
    }
    else {
      reportList.value.push(...mockData.lists)
    }

    console.log(`数据加载成功`, reportList.value.length)
  }
  catch (error) {
    console.error('获取列表失败', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  }
  finally {
    currentTab.isLoading = false
    if (currentTab.isRefreshing) {
      currentTab.isRefreshing = false
    }
  }
}
getReportList()

// 跳转详情
function handleToDetail(id: number) {
  uni.navigateTo({
    url: `/subPackages/accompany/reportDetail?id=${id}&type=${type.value}`,
  })
}
</script>

<template>
  <view class="page-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Report
        </view>
      </template>
    </TopNav>

    <scroll-view class="list" scroll-y :show-scrollbar="false" :refresher-enabled="true" :refresher-threshold="100"
      refresher-default-style="white" :refresher-triggered="parData.isRefreshing" refresher-background="transparent"
      @scrolltolower="() => handleScrolltolower()" @refresherrefresh="() => handleRefresh()">
      <ReportItem v-for="item in reportList" :key="item.id" :item="item" @click="handleToDetail(item.id)" />

      <!-- 空状态处理 -->
      <ScrollEmpty :is-loading="parData.isLoading" :is-last-page="parData.isLastPage" :list="reportList">
        <image src="/static/mine/Notification.png" class="empty-image" mode="aspectFit" />
        <text class="empty-title">
          Dialogue with History
        </text>
        <text class="empty-text">
          You need to create a new conversation, and the content after the conversation will be recorded here
        </text>
      </ScrollEmpty>
    </scroll-view>
  </view>

</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;
}

.list {
  flex: 1;
  overflow: hidden;
  padding-top: 40rpx;
  box-sizing: border-box;
  font-family: var(--font-family-regular);

  .empty-image {
    width: 448rpx;
    height: 448rpx;
    margin-bottom: 36rpx;
  }

  .empty-title {
    font-size: $font-size-heading-ml;
    font-family: var(--font-family-regular);
    font-weight: $font-weight-bold;
    color: #1d2939;
    line-height: 68rpx;
    margin-top: 100rpx;
  }

  .empty-text {
    font-size: $font-size-heading-sm;
    font-family: var(--font-family-regular);
    font-weight: $font-weight-regular;
    color: #98a2b3;
    margin-top: 32rpx;
  }
}
</style>
