<script setup>
import { ref } from 'vue'
import Navbar from '@/components/navbar/index.vue'
import CameraCard from './components/camera-card.vue'
import AddBtn from './components/add-btn.vue'
import UploadFile from './components/upload-file.vue'
// import Filter from './components/filter.vue'
// import Swiper from './components/swiper.vue'
// import SelectFileType from './components/select-file-type.vue'
//打开选择视频/相册弹窗
const showMedia = ref(false)
const showSelectAlert = ref(false)
const current = ref(0)
const fileList = ref([
  {
    url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg'
  }
])
const swiperList = ref([
  { url: 'https://xinliushijie.guest.geekdanceshop.com/upload/common/images/20250705/20250705114213175168693367482.JPG', icon: false },
  { url: 'https://xinliushijie.guest.geekdanceshop.com/upload/common/images/20250705/20250705114213175168693367482.JPG', icon: true },
])
function handleClick(e) {

}
function onChange() {

}
function selectImg() {
  // uni.chooseVideo({
  //   sourceType: ['camera', 'album'],
  //   success: function (res) {
  //     console.log('chooseRes',res)
  //     // self.src = res.tempFilePath;
  //   }
  // })
}
function openMedia() {
  showMedia.value = true
  showSelectAlert.value = true
}
function close() {
  console.log('关闭了')
  showMedia.value = false
}
function typeChange(e) {
  console.log('typeChange', e)
}
</script>

<template>
  <view class="new-post">
    <Navbar title="New Post">
      <template #right>
       <text class="title">Next</text>
      </template>
    </Navbar>
    <!-- <view class="tip flex-row-item justify-between align-center">
      <text>You have granted Gigbib access to some photos and videos.</text>
      <text>Manage</text>
    </view> -->
    <!-- 图片合集 -->
    <view class="flex-row-item camera-list">
      <view v-for="(item,index) in swiperList" :key="index" style="margin-right:20rpx;">
        <CameraCard :active="index==current"/>
      </view>
    </view>
    <view style="padding:0 32rpx;">
      <wd-swiper
        v-model:current="current"
        class="wdSwiperHome"
        :list="swiperList"
        value-key="url"
        title-key="title"
        custom-text-style="color:#000000"
        :indicator="{ type: 'dots-bar' }"
        :height="343"
        @click="handleClick"
        @change="onChange"
      >
        <template #icon="{ item }">
          <!-- <view v-if="item.icon" class="play-icon">
            <wd-icon name="play" size="36px" color="#888" />
          </view> -->
        </template>
      </wd-swiper>
    </view>
    <!-- <Swiper :index="current" :swiperList="swiperList" @click="handleClick" @change="onChange"/> -->
    <AddBtn @click="openMedia"/>  
    <UploadFile :show="showMedia" :showSelectAlert="showSelectAlert" @close="close" @closeSelect="showSelectAlert=false"/>
    <!-- <SelectFileType :show="true" @click="typeChange"></SelectFileType>   -->
     <!-- 滤镜 -->
    <!-- <Filter /> -->
  </view>
</template>

<style lang="scss" scoped>
.title{
  color:#98A2B3;
  font-size:28rpx;
}
.wdSwiperHome{
    ::v-deep .wd-swiper__track{
        border-radius: 8rpx !important;
    }
}
.camera-list {
  margin:20rpx 32rpx;
  overflow-x: auto;
  overflow-y: hidden;
  /* 隐藏滚动条但保留滚动功能 */
  // overflow: auto;
  /* Firefox */
  scrollbar-width: none;
  /* IE/Edge */
  -ms-overflow-style: none; 
}
/* 隐藏WebKit内核滚动条 */
.camera-list::-webkit-scrollbar {
  display: none; 
}
//  .tip{
//     background:#EAECF0;
//     padding:24rpx;
//     text:nth-child(1){
//         color:#667085;
//         font-size:$font-size-lg;
//     }
//     text:nth-child(2){
//         margin-left:34rpx;
//     }
// }
</style>
