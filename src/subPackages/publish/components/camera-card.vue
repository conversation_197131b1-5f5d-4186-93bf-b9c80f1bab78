<template>
    <view class="content">
        <view v-if="active" class="mask"></view>
        <image
            src=""
            mode="scaleToFill"
        />
    </view>
</template>
<script setup>
import { defineProps } from 'vue'
const props = defineProps({
    active:{
        type:<PERSON>olean,
        default:false
    }
})
</script>
<style lang="scss" scoped>
.content{
    background:rgba(0,0,0,.1);
    width:136rpx;
    height:136rpx;
    border-radius:20rpx;
    position: relative;
    .mask{
        position: absolute;
        right:0;
        top:0;
        background:rgba(0,0,0,.1);
        width:136rpx;
        height:136rpx;
        z-index: 99;
        border-radius:20rpx;
    }
    image{
        width:136rpx;
        height:136rpx;
        background:pink;
        border-radius:20rpx;
    }
}
</style>