<template>
    <view style="padding:0 32rpx;">
      <wd-swiper
        v-model:current="index"
        class="wdSwiperHome"
        :list="swiperList"
        value-key="url"
        title-key="title"
        custom-text-style="color:#000000"
        :indicator="{ type: 'dots-bar' }"
        :height="343"
        @click="handleClick"
        @change="onChange"
      >
        <template #icon="{ item }">
          <!-- <view v-if="item.icon" class="play-icon">
            <wd-icon name="play" size="36px" color="#888" />
          </view> -->
        </template>
      </wd-swiper>
    </view>
</template>
<script setup lang="ts">
import { ref, defineEmits,defineProps} from 'vue'
const props = defineProps({
  swiperList: {
    type: Array,
    default:[]
  },
  index:{
    type: Number,
    default: 0
  }
})
// const emit = defineEmits(['handleClick','onChange'])
const handleClick = (item: any) => {
//   emit('handleClick')
}
const onChange = (index: number) => {
//   emit('onChange')
}
</script>