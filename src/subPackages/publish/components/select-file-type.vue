<template setup>
    <view v-if="show">
        <view class="slef-mask" @click="cancel"></view>
        <view class="select-file-type">
            <view class="select-file">
                <view class="item" v-for="(item,index) in data" :key="index" @click.stop="checkNumRange(item,index)">{{ item.name }}</view>
            </view>
            <view class="cancel" @click.stop="cancel">cancel</view>
        </view>
    </view>
</template>
<script setup>
import { ref, defineEmits, defineProps } from 'vue'
const props = defineProps({
    show: {
        type: Boolean,
        default: false
    },
    data:{
        type: Array,
        default: []
    }
})
const emit = defineEmits(['click','cancel'])
const checkNumRange = (data,index) => {
    emit('click',index)
    // selectFile(data.name)

}
const cancel = () => {
    emit('cancel')
}

</script>
<style lang="scss" scoped>
.select-file-type{
    z-index:999;
    margin:20rpx;
    position: fixed;
    bottom:0;
    width:calc(100% - 40rpx);
}
.select-file{
    border-radius:20rpx;
    background:$color-white;
    .item{
        text-align:center;
        padding:40rpx 0;
        color:#3e74ca;
    }
    view:nth-child(1){
        border-bottom:1px solid #f5f5f5;
    }
}
.cancel{
    background:$color-white;
    color:#3e74ca;
    padding:40rpx 0;
    border-radius:20rpx;
    text-align:center;
    margin-top:10rpx;
}
</style>