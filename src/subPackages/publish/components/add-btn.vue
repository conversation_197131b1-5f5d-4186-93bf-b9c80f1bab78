<script setup>
import {defineEmits} from 'vue'
const emit = defineEmits(['click'])
const change = () => {
    emit('click')
}
</script>
<template>
    <view class="media flex-row-item flex-col" @click="change">
        <image
        src="@/subPackages/baby/static/baby/media.png"
        mode="scaleToFill"
        />
        <view class="content">
        No media
        </view>
        <view class="remark">
        Your photos and videos will appear here
        </view>
    </view>
</template>
<style lang="scss" scoped>
.media{
    // margin-top:220rpx;
    margin-top:120rpx;
    color:#98A2B3;
    image{
        width:184rpx;
        height:184rpx;
    }
    .content{
        font-family: var(--font-family-title);
        margin-top:40rpx;
        font-size:40rpx;
    }
    .remark{
        margin-top:8rpx;
    }
}
</style>