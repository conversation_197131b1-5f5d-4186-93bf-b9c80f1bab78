<template>
   <view v-if="show">
    <!-- 选择视频或照片 -->
     <SelectFileType :show="showSelectAlert" :data="typeItem" @click="selectFileType" @cancel="closeSelectAlert"></SelectFileType>  
   <!-- 打开相机或相册 -->
     <SelectFileType :show="showOperateAlert" :data="fileSourceItem" @click="selectFileSource" @cancel="cancel"></SelectFileType>  
   </view>
</template>
<script setup lang="ts">
import {ref, defineProps, defineEmits} from 'vue'
import SelectFileType from './select-file-type.vue'
// const showSelectAlert = ref<Boolean>(true)
const showOperateAlert = ref<Boolean>(false)
const fileType = ref<String>('video')
const props = defineProps({
    show:{
        type:Boolean,
        default:false
    },
    showSelectAlert:{
        type:Boolean,
        default:false
    }
})
//选择文件类型
const emit = defineEmits(['close','closeSelect'])
const selectFileType = (e:Number)=>{
//    console.log('selectFile',typeItem.value[e].name)
    fileType.value = typeItem.value[e].name
    emit('closeSelect')
    // showSelectAlert.value = false
    showOperateAlert.value = true
}
// 选择文件来源
const selectFileSource = (e:String)=>{
    if(fileType.value == 'video'){
        selectVideo(e)
    }else{
        selectPhoto(e)
    }
    showOperateAlert.value = false
    emit('close')
}
const closeSelectAlert = () => {
    emit('closeSelect')
}
// 选择照片
const selectPhoto = (e:Number)=>{
    console.log('获取到的类型',e)
    uni.chooseImage({
        sourceType: [fileSourceItem.value[e].name],
        success: function (res) {
            
        }
    })
}
const selectVideo = (e:Number)=>{
     uni.chooseVideo({
        sourceType: [fileSourceItem.value[e].name],
        success: function (res) {
            // self.src = res.tempFilePath;
        }
    })
}
const cancel = ()=>{
    showSelectAlert.value = false
}
const typeItem = ref([
    {
        name:'video'
    },
    {
        name:'photo'
    }
])
const fileSourceItem = ref([
    {
        name:'camera'
    },
    {
        name:'album'
    }
])
</script>