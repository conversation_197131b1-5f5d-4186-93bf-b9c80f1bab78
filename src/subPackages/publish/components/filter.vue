<template>
   <view>
   <wd-tabs v-model="tab">
        <block v-for="item in tabs" :key="item">
            <wd-tab :title="`${item}`" :name="item">
            <view class="content">内容{{ item }}</view>
            </wd-tab>
        </block>
    </wd-tabs>
    <view class="filter-main flex-row-item">
        <image
            v-for="(item,index) in 6"
            src=""
            mode="scaleToFill"
        />
    </view>
   </view>
</template>
<script setup>
const tabs = ref(['这', '是', '一', '个', '例子'])
const tab = ref('例子')
</script>
<style lang="scss" scoped>
.content {
  line-height: 120px;
  text-align: center;
}
.filter-main{
    flex-wrap: nowrap;
    overflow-x: auto;
   image{
        background:pink;
        width:220rpx;
        height:220rpx;
        margin-right:20rpx;
        flex-shrink: 0;
   }
}
</style>