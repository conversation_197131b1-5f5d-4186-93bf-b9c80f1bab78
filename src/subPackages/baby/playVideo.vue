
<script setup>
    import { ref, onMounted, onUnmounted } from 'vue'
    import { onLoad } from '@dcloudio/uni-app';
    const screenWidth = ref(0)
    const screenHeight = ref(0)
    const show = ref(false)
    const isFullScreen = ref(false)

    const onVideoEnd = () => {
        console.log('视频播放结束')
    }
    const handlePlay = () => {
        console.log('视频开始播放')
    }
    //视频链接
    const url = ref('')
    onMounted(() => {
        const systemInfo = uni.getSystemInfoSync();
        screenWidth.value = systemInfo.screenWidth;
        screenHeight.value = systemInfo.screenHeight-systemInfo.statusBarHeight;
    })
    onLoad((options) => {
        url.value = options.url
    })
</script>
<template>
    <view class="video flex-row-item justify-center">
        <view>
            <video ref="videoPlayer" :style="{'width':screenWidth+'px','height':screenHeight+'px'}" :show-fullscreen-btn="false" autoplay @ended="onVideoEnd" id="myVideo" :autoplay="true" :controls="true"  class="blogger__main-image blogger__main-image--1 tn-margin-bottom-sm" :src="url" @play="handlePlay"></video>
        </view>
    </view>
</template>
<style lang="scss" scoped>
	.video{
		background:#000;
		min-height: 100vh;
		overflow: hidden;
	}
</style>