<script setup>
import { defineEmits, defineProps } from 'vue'
import noSelectIcon from '@/subPackages/baby/static/baby/noSelectIcon.png'
import selectIcon from '@/subPackages/baby/static/baby/selectIcon.png'

const props = defineProps({
  // 展示弹窗
  option: {
    type: Object,
    default: {},
  },
  index: {
    type: Number,
    default: 0.1,
  },
  // 是否展示单选框
  show: {
    type: Boolean,
    default: false,
  },
  // 选中的id
  current: {
    type: Array,
    default: [],
  },
})
const emit = defineEmits(['click'])
function change() {
  emit('click')
}
</script>

<template>
  <view class="flex-row-item rowCard">
    <image v-if="show" class="select-icon" :src="current.includes(option.id) ? selectIcon : noSelectIcon" />
    <view class="function-item flex-row-item justify-between" :style="{ 'animation-delay': `${(index + 1) / 10}s` }">
      <view class="flex-row-item">
        <image class="function-icon" :src="option.emoji" />
        <view class="function-info">
          <view class="function-title">
            {{ option.title }}
          </view>
          <view v-if="option.memberCount" class="member-count">
            <text>Can View:</text> {{ option.memberCount }}
          </view>
        </view>
      </view>
      <view style="width:20%;" />
      <view class="arrow flex-row-item" @click.stop="change">
        <image src="@/subPackages/baby/static/baby/arrow-right.png" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .select-icon{
    width:40rpx;
    height:40rpx;
    margin-right:52rpx;
  }
  .rowCard{
    margin-bottom:40rpx;
    position: relative;
  }
 .function-item {
    box-shadow: 5px 4px 5px 0px #9E774D24;
    padding:24rpx;
    transition: background-color 0.2s ease;
    border-radius:32rpx;
    flex:1;
}
.function-icon {
    width: 96rpx;
    height: 96rpx;
    border-radius: 24rpx;
    background-color:#FFF5E3;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    flex-shrink: 0;
}
.function-item:last-child {
    border-bottom: none;
}

.function-item:hover {
    background-color: #f9f9f9;
}
.member-count {
    font-size: $font-size-base;
    margin-top:10rpx;
    color:#000;
    text{
        color: #667085;
    }
}

.arrow {
    color: #999;
    width: 20%;
    text-align:right;
    color: #999;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    justify-content: center;
    image{
        width: 24rpx;
        height: 24rpx;
        margin-left:12rpx;
    }
}
.function-info {
    // display: flex;
    // align-items: center;
}

.function-title {
    font-size: $font-size-lg;
    font-family:var(--font-family-title);
}
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
.function-item {
    animation-duration: 0.4s;
    animation-timing-function: ease-out;
    animation-fill-mode: both;
    animation-name: fadeIn;
}
.right{
    flex:1;
    margin-left:20rpx;
    justify-content: flex-end;
}

// .function-item:nth-child(1) {
//     animation-delay: 0.1s;
//     animation-name: fadeIn;
// }
</style>
