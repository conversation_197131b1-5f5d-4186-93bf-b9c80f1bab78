<template>
    <view class="add-baby">
        <view class="mask" @click="submit"></view>
        <wd-floating-panel v-model:height="height" :anchors="[100, 300]"  class="high-z-index" :contentDraggable="false" :safeAreaInsetBottom="true" @heightChange="heightChange">
            <view class="inner-content">
                <view v-for="(item,index) in memberList" :key="index" class="flex-row-item justify-between member-item" @click="change(item.id)">
                    <view>{{item.name}}</view>
                    <image class="select-icon" :src="current.includes(item.id) ? selectIcon : noSelectIcon" />
                </view>
            </view>
        </wd-floating-panel>
    </view>
</template>
<script setup>
import { ref } from 'vue' 
import { defineProps, defineEmits } from 'vue'
import selectIcon from '@/subPackages/baby/static/baby/selectIcon.png'
import noSelectIcon from '@/subPackages/baby/static/baby/noSelectIcon.png'
const height = ref(300)
const memberList = ref([
    {name:'<PERSON>',id:1},
    {name:'<PERSON>',id:2},
    {name:'<PERSON><PERSON>',id:3},
    {name:'<PERSON>e',id:4},
    {name:'Arroba',id:5}
])
const emit = defineEmits(['change']);
const heightChange = ({height}) => {
    if(height<=100){
        submit()
    }
}
const current = ref([])
// 选择人员操作
const change = (id) => {
    if(!current.value.includes(id)){
        current.value.push(id)
    }else{
        const index = current.value.indexOf(id)
        current.value.splice(index,id)
    }
    // console.log(current.value)
}
const submit = () => {
    emit('change',current.value)
}
</script>
<style lang="scss" scoped>
:v-deep .wd-radio{
    margin-top:20px !important;
}
.mask{
    background:rgba(0,0,0,.3);
    width:100%;
    height:100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index:98;
}
.high-z-index{
    z-index: 9999;
}
.inner-content{
    padding:0 68rpx;
}
.member-item{
    margin-bottom:34rpx;
    padding:14rpx 0;
    color:#667085;
}
.select-icon{
    width:40rpx;
    height:40rpx;
}
</style>