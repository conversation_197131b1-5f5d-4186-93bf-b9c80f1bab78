<script setup>
import { onMounted, ref } from 'vue'
import Modal from '@/components/modal/index.vue'
import DeleteBtn from '@/subPackages/baby/components/delete-btn.vue'
import fatherIcon from '@/subPackages/baby/static/baby/father.png'
import motherIcon from '@/subPackages/baby/static/baby/mother.png'
import AddMember from './components/add-member.vue'
import RowCard from './components/row-card.vue'
// 删除按钮展示
const deleteBtnShow = ref(false)
// 删除弹窗展示
const deleteAlertShow = ref(false)
// 功能选项数据
const options = ref([])
function getList() {
  options.value = [
    {
      emoji: motherIcon,
      title: 'Kitty',
      description: 'gigbib、deinna、laati、kiiry',
      type: 1,
      id: 1,
    },
    {
      emoji: motherIcon,
      title: 'Emma',
      description: 'gigbib、deinna、laati、kiiry、gigbib、deinna、laati、kiiry',
      id: 2,
    },
    {
      emoji: motherIcon,
      title: 'Linni',
      memberCount: 'gigbib、deinna、laati、kiiry、gigbib、deinna、laati、kiiry',
      id: 3,
    },
    {
      emoji: fatherIcon,
      title: 'Jone',
      memberCount: 'gigbib、deinna、laati、kiiry',
      id: 4,
    },
  ]
}
function selectRow(id) {
  console.log('当前获取到的数据', id)
  addMemberShow.value = true
}
function deleteBaby() {
  reset()
  console.log('当前获取到的删除数据', activeRowItem.value)
}

// 切换选择的option
const activeRowItem = ref([])
function changeBtnIndex(id) {
  if (!deleteBtnShow.value)
    return
  if (!activeRowItem.value.includes(id)) {
    activeRowItem.value.push(id)
  }
  else {
    const index = activeRowItem.value.indexOf(id)
    activeRowItem.value.splice(index, id)
  }
}

// 是否展示新增成员弹窗
const addMemberShow = ref(false)

// 获取到选中的成员
function addMemberChange(data) {
  console.log('获取到选中的成员', data)
  addMemberShow.value = false
}
function reset() {
  deleteAlertShow.value = false
  deleteBtnShow.value = false
}
onMounted(() => {
  getList()
})
</script>

<template>
  <view>
    <Navbar title="Member Management" />
    <!-- 功能选项区域 -->
    <view class="section">
      <!-- 信息选项 -->
      <view v-for="(option, index) in options" :key="index" @click="changeBtnIndex(option.id)">
        <RowCard :option="option" :current="activeRowItem" :show="deleteBtnShow" :index="index" @click="selectRow(option.id)" />
      </view>
    </view>
    <!-- 删除按钮 -->
    <DeleteBtn v-if="!deleteBtnShow" title="Delete Member" @click="deleteBtnShow = true" />
    <!-- 确认删除按钮 -->
    <DeleteBtn v-else title="Delete" @click="deleteAlertShow = true" />
    <!-- 新增成员弹窗 -->
    <AddMember v-if="addMemberShow" @change="addMemberChange" />
    <!-- 确认删除弹窗 -->
    <Modal :visible="deleteAlertShow" title="Confirm deletion of baby’s infor mation" @confirm="deleteBaby" @cancel="reset">
      <view class="delete-modal">
        <image />
      </view>
    </Modal>
  </view>
</template>

<style lang="scss" scoped>
/* 功能区域 */
.section {
    padding:32rpx;
}
.delete-modal{
  width:100%;
  background:pink;
  height:670rpx;
  border-radius:20rpx;
}
</style>
