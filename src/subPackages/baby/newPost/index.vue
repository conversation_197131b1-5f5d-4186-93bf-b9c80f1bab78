<script setup>
import Navbar from '@/components/navbar/index.vue'
</script>

<template>
  <view class="new-post">
    <Navbar title="New Post" />
    <view class="tip flex-row-item justify-between align-center">
      <text>You have granted Gigbib access to some photos and videos.</text>
      <text>Manage</text>
    </view>
    <view class="media flex-row-item flex-col">
      <image
        src="@/subPackages/baby/static/baby/media.png"
        mode="scaleToFill"
      />
      <view class="content">
        No media
      </view>
      <view class="remark">
        Your photos and videos will appear here
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
 .tip{
    background:#EAECF0;
    padding:24rpx;
    text:nth-child(1){
        color:#667085;
        font-size:$font-size-lg;
    }
    text:nth-child(2){
        margin-left:34rpx;
    }
}
.media{
    margin-top:220rpx;
    color:#98A2B3;
    image{
        width:184rpx;
        height:184rpx;
    }
    .content{
        font-family: var(--font-family-title);
        margin-top:40rpx;
        font-size:40rpx;
    }
    .remark{
        margin-top:8rpx;
    }
}
</style>
