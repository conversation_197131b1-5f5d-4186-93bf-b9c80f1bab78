<template>
    <view>
        <navBar :title="title"></navBar>
        <view class="flex-row-item justify-between height" @click="show = true">
            <text class="title">{{title}}</text>
            <view class="flex-row-item right">
                <text>{{ heightData || title==='Height' ? 'Please enter height' : 'Please enter weight'}}</text>
                <text>{{title==='Height' ? 'CM' : 'KG'}}</text>
            </view>
        </view>
        <view class="chart-container">
            <EchartsComponents :screenWidth="screenWidth" />
        </view>
        <wd-action-sheet v-model="show" :title="title==='Height' ? 'Height（cm）' : 'Weight (kg)'"  @close="heightSubmit">
            <view class="a" style="max-height: 300px;overflow: scroll;">
                <view class="heightItem" v-for="(item,index) in (title==='Height' ? heightItem : weightItem )" :style="{'color':item.num == activeId ? '#1D232E' : '#98A2B3'}" @click="activeId = item.num">{{ item.num }}</view>
            </view>
        </wd-action-sheet>
    </view>
</template>
<script setup>
import {onMounted, ref} from 'vue'
import navBar from './components/navBar.vue'
import EchartsComponents from './components/echarts.vue'
// import HeightSelect from './components/height-select.vue'
import { onLoad } from '@dcloudio/uni-app';
// 判断是身高还是体重页面
const title = ref('')
const showHeightSelect = ref(false)
const heightItem = ref([
    { num: 20 },
    { num: 21 },
    { num: 22 },
    { num: 23 },
    { num: 24 }
])

const weightItem = ref([
    { num: 20 },
    { num: 21 },
    { num: 22 },
    { num: 23 },
    { num: 24 }
])

const activeId = ref(20)
const heightData = ref('')
const heightSubmit = () => {
    console.log('身高提交', activeId.value)
    heightData.value = activeId.value
}
// import * as echarts from 'echarts'
// import LEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart'
// const chartRef = ref(null)
// const option = {
//     backgroundColor: '#ffffff',
//     title: {
//         text: "The baby's height is increasing.",
//         left: 'left',
//         textStyle: {
//         color: '#333',
//         fontSize: 16,
//         fontWeight: 'bold'
//         },
//         padding: [10, 0, 0, 20]
//     },
//     grid: {
//         left: '5%',
//         right: '5%',
//         bottom: '10%',
//         top: '20%',
//         containLabel: true
//     },
//     xAxis: {
//         type: 'category',
//         data: ['8', '9', '10', '11', '12', '13', '14','8', '9', '10', '11', '12', '13', '14'],
//         axisLine: {
//         lineStyle: {
//             color: '#999'
//         }
//         },
//         axisLabel: {
//         color: '#666'
//         },
//         name: '月龄',
//         nameLocation: 'end',
//         nameTextStyle: {
//         padding: [5, 0, 0, 0]
//         }
//     },
//     yAxis: {
//         type: 'value',
//         min: 0,
//         max: 60,
//         interval: 10,
//         axisLine: {
//         lineStyle: {
//             color: '#999'
//         }
//         },
//         splitLine: {
//         lineStyle: {
//             color: '#eee'
//         }
//         },
//         axisLabel: {
//         formatter: '{value} cm',
//         color: '#666'
//         }
//     },
//     series: [
//         {
//         name: '身高',
//         type: 'line',
//         data: [35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48],
//         smooth: true,
//         symbol: 'circle',
//         symbolSize: 8,
//         lineStyle: {
//             color: '#ff5252',
//             width: 3
//         },
//         itemStyle: {
//             color: '#ff5252',
//             borderWidth: 2,
//             borderColor: '#fff'
//         },
//         markPoint: {
//             symbol: 'pin',
//             symbolSize: 50,
//             data: [
//             {
//                 name: '48cm',
//                 value: '48cm',
//                 xAxis: '14',
//                 yAxis: 48,
//                 label: {
//                 formatter: '48cm',
//                 backgroundColor: '#ff9d66',
//                 padding: [6, 10],
//                 borderRadius: 10,
//                 position: 'top',
//                 color: '#fff',
//                 fontSize: 14
//                 }
//             }
//             ]
//         }
//         }
//     ],
//     tooltip: {
//         trigger: 'axis',
//         formatter: '{b}日: {c}cm',
//         backgroundColor: 'rgba(255,255,255,0.9)',
//         borderColor: '#ddd',
//         borderWidth: 1,
//         textStyle: {
//         color: '#333'
//         },
//         padding: 10
//     }
// };
// // echarts的宽度
const screenWidth = ref(0)
const show = ref(false)

const close = () => {
  console.log('close')
}
onMounted( ()=>{
    // 组件能被调用必须是组件的节点已经被渲染到页面上
    const systemInfo = uni.getSystemInfoSync();
    screenWidth.value = systemInfo.screenWidth - 40;
})
onLoad((options) => {
    title.value = options.type
})
</script>
<style lang="scss" scoped>
    .chart-container{
        box-shadow: 0px 1px 8px 0px rgba(110, 110, 110, 0.1);
        border-radius: 32rpx;
        margin:0 32rpx;
    }
    .height{
        background:$color-white;
        padding:30rpx 24rpx; 
        box-shadow: 10px 20px 46px 0px rgba(158, 119, 77, 0.14);
        margin:40rpx 32rpx;
        border-radius:32rpx;
        .title{
            font-family:var(--font-family-title);
        }
        .right{
            font-size:$font-size-md;
            text:nth-child(1){
                color:rgba(102, 112, 133, 1);
                margin-right:20rpx;
            }
            text:nth-child(2){
                color:rgba(29, 35, 46, 1);
                font-family:var(--font-family-title);
            }
        }
    }
    .a::-webkit-scrollbar{
        display: none;
    }
    .heightItem{
        width: 100%;
        text-align: center;
        padding: 10rpx 0;
        color:#98A2B3;
    }
</style>
