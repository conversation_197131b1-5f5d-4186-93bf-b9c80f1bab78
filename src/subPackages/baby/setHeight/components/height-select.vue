<template>
    <view v-if="show" class="add-baby">
        <!-- <view class="mask" @click="handleChange"></view> -->
        <wd-select-picker class="high-z-index" label="基本用法" v-model="value" :columns="columns" @change="handleChange"></wd-select-picker>
    </view>
</template>
<script setup>
import { ref } from 'vue' 
import { defineProps, defineEmits } from 'vue'
const props = defineProps({
    // 展示弹窗
    show: {
        type: Boolean,
        default: false
    }
})

const columns = ref([
  {
    value: '101',
    label: '男装'
  },
  {
    value: '102',
    label: '奢侈品'
  },
  {
    value: '103',
    label: '女装'
  }
])
const value = ref(['101'])
const emit = defineEmits(['change']);

const handleChange = () => {
    // emit('change',time)
}
</script>
<style lang="scss" scoped>
:v-deep .wd-radio{
    margin-top:20px !important;
}
.mask{
    background:rgba(0,0,0,.3);
    width:100%;
    height:100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index:999;
}
.high-z-index{
    z-index: 9999;
}
.inner-content{
    padding:0 68rpx;
}
</style>