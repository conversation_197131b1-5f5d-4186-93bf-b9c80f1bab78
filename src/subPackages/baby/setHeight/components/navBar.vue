<template>
    <wd-navbar :title="title" safeAreaInsetTop custom-style="background-color: transparent !important;" @click-left="handleClickLeft">
         <template  #left>
           <view class="cancel">Cancel</view>
        </template>
        <template v-if="!title" #title>
            <view class="search-box">
                <wd-drop-menu>
                    <wd-drop-menu-item :icon-size="20" v-model="value1" :options="option" @change="handleChange"></wd-drop-menu-item>
                </wd-drop-menu>
            </view>
        </template>
        <template  #right>
           <view class="saveBtn">Save</view>
        </template>
    </wd-navbar>
</template>
<script setup>
import { ref, defineProps } from 'vue'
const props = defineProps({
    // 展示弹窗
    title: {
        type: String,
        default: ''
    }
})
const value1 = ref(0)
const option = ref([
  { label: 'Choose a baby', value: 0 },
  { label: 'Emma', value: 1 },
  { label: 'Kitty', value: 2 }
])
const handleChange = (value) => {
    
}
const handleClickLeft = () => {
    uni.navigateBack()
}
</script>

<style lang="scss" scoped>
::v-deep.wd-drop-item__option{
    &.is-active{
        .wd-drop-item__title{
            color: #000;
            font-weight: bold;
        }
    }
    .wd-icon-check{
        display: none;
    }
    .wd-drop-item__title{
        width: 100%;
        text-align: center;
        color: #888;
    }
}
.cancel{
    color: rgba(51, 51, 51, 1);
    font-size:$font-size-md;
}
.search-box{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.saveBtn{
    color: $color-white;
    background: linear-gradient(92.74deg, #FFB173 9.43%, #FF9844 96.54%);
    font-size:$font-size-heading-sm;
    padding:0 24rpx;
    border-radius: 40rpx;
    height:68rpx;
    line-height:68rpx;
    box-shadow: 0px 8px 16px 0px #FD665559;
}
</style>