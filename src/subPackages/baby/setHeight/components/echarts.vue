<template>
    <view style="height:750rpx" :style="{'width':screenWidth+'px'}"><l-echart ref="chartRef"></l-echart></view>
</template>
<script setup>
import {onMounted, ref} from 'vue'
import * as echarts from 'echarts'
import LEchart from '@/uni_modules/lime-echart/components/l-echart/l-echart'
const props = defineProps({
    screenWidth: {
        type: Number,
        default: 0
    }
})
const chartRef = ref(null)
const data =  ['8', '9', '10', '11', '12', '13', '14','8', '9', '10', '13', '14','8', '9', '10']
const option = {
    backgroundColor: '#ffffff',
    title: {
        text: "The baby's height is increasing.",
        left: 'left',
        textStyle: {
        color: '#333',
        fontSize: 16,
        fontWeight: 'bold'
        },
        padding: [10, 0, 0, 20]
    },
    grid: {
        left: '5%',
        right: '5%',
        bottom: '10%',
        top: '20%',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data,
        axisLine: {
            lineStyle: {
                color: '#999'
            }
        },
        axisLabel: {
            color: '#666',
            interval: 0, // 强制显示所有标签
            rotate: 45,  // 标签倾斜45度避免重叠
            formatter: '{value}' // 自定义标签格式
        },
        name: '月龄',
        nameLocation: 'end',
        nameTextStyle: {
        padding: [5, 0, 0, 0]
        }
    },
    yAxis: {
        type: 'value',
        min: 0,
        max: 60,
        interval: 10,
        axisLine: {
        lineStyle: {
            color: '#999'
        }
        },
        splitLine: {
        lineStyle: {
            color: '#eee'
        }
        },
        axisLabel: {
        formatter: '{value} cm',
        color: '#666'
        }
    },
    dataZoom: [
        {
            type: "inside",
            start: 0,
            end: data.length <= 10 ? 100 : (10/data.length)*100
        }
    ],
    // lenth 10  end 60  res 6
    // lenth 10  end 30  res 4 
    // lenth 10  end 20  res 3
    series: [
        {
        name: '身高',
        type: 'line',
        data: [35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48,35, 38, 39, 40, 42, 44, 48],
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
            color: '#ff5252',
            width: 3
        },
        itemStyle: {
            color: '#ff5252',
            borderWidth: 2,
            borderColor: '#fff'
        },
        markPoint: {
            symbol: 'pin',
            symbolSize: 50,
            data: [
            {
                name: '48cm',
                value: '48cm',
                xAxis: '14',
                yAxis: 48,
                label: {
                formatter: '48cm',
                backgroundColor: '#ff9d66',
                padding: [6, 10],
                borderRadius: 10,
                position: 'top',
                color: '#fff',
                fontSize: 14
                }
            }
            ]
        }
        }
    ],
    tooltip: {
        trigger: 'axis',
        formatter: '{b}日: {c}cm',
        backgroundColor: 'rgba(255,255,255,0.9)',
        borderColor: '#ddd',
        borderWidth: 1,
        textStyle: {
        color: '#333'
        },
        padding: 10
    }
};
// echarts的宽度
// const screenWidth = ref(0)
onMounted( ()=>{
    // 组件能被调用必须是组件的节点已经被渲染到页面上
   
    // screenHeight.value = systemInfo.screenHeight-systemInfo.statusBarHeight;
    setTimeout(async()=>{
        if(!chartRef.value) return
        const myChart = await chartRef.value.init(echarts)
        myChart.setOption(option)
    },300)
})
</script>