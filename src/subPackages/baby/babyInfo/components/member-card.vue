<script setup>
import { defineEmits, defineProps, ref } from 'vue'

const props = defineProps({
  // 展示弹窗
  type: {
    type: String,
    default: '',
  },
  userStats: {
    type: Object,
    default: {},
  },
})
const emit = defineEmits(['add'])
function add() {
  emit('add')
}
</script>

<template>
  <view>
    <!-- 添加成员卡片 -->
    <view v-if="type === 'add'" class="member-card add-card" @click="add">
      <!-- <view class="add-icon">+</view> -->
      <image class="add-icon" src="../../static/baby/add-member-icon.png" />
      <view class="stats">
        Add
      </view>
      <view>a member</view>
    </view>
    <!-- 妈妈卡片 -->
    <view v-else class="member-card mom-card">
      <image />
      <view class="role">
        Mom
      </view>
      <view class="stats">
        Visited {{ userStats.visitCount }} times
      </view>
      <view class="timestamp">
        {{ userStats.lastVisit }}
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.member-card {
    background-color: $color-white;
    border-radius: 40rpx;
    padding: 32rpx;
    position: relative;
    overflow: hidden;
    width:208rpx;
    height:276rpx;
    margin-right:32rpx;
    color:$color-white;
}

.mom-card {
    background:$color-primary;
    text-align: center;
    image{
        width: 100rpx;
        height: 100rpx;
        background:pink;
        border-radius: 50%;
    }
    .role {
        font-size: $font-size-lg;
        font-weight: 400;
        margin: 20rpx 0 16rpx;
    }
    .stats {
        font-size: $font-size-mdx;
        margin-top: 8px;
    }

    .timestamp {
        font-size: $font-size-base;
        font-weight: 400;
    }
}

.add-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    // justify-content: center;
    transition: all 0.3s ease;
    background:$color-background-light;
    .add-icon {
        width:100rpx;
        height:100rpx;
        margin-bottom: 8px;
        font-weight: bold;
    }
    .stats {
        font-size: $font-size-mdx;
        margin: 20rpx 0 16rpx;
    }
}
</style>
