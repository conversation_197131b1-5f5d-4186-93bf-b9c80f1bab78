<script setup>
import { defineEmits, defineProps, ref } from 'vue'

const props = defineProps({
  // 展示弹窗
  show: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['close'])
const inputValue = ref('')
function submitBabyName() {
  emit('close')
  if (inputValue.value) {
    // 提交baby名字
    uni.navigateTo({
      url: '/subPackages/baby/babyInfo/index',
    })
  }
}
const value = ref(1)
</script>

<template>
  <view v-if="show" class="add-baby">
    <view class="mask" @click="submitBabyName" />
    <wd-floating-panel class="high-z-index" :content-draggable="false" :safe-area-inset-bottom="true">
      <view class="inner-content">
        <wd-radio-group v-model="value" shape="dot" checked-color="#F8852D" size="large" @change="change">
          <wd-radio :value="1">
            Mother
          </wd-radio>
          <wd-radio :value="2">
            Father
          </wd-radio>
        </wd-radio-group>
        <view class="input-box">
          <input ref="myInput" v-model="inputValue" class="myInput" type="text" placeholder="Add a baby">
        </view>
      </view>
    </wd-floating-panel>
  </view>
</template>

<style lang="scss" scoped>
:v-deep .wd-radio{
    margin-top:20px !important;
}
.mask{
    background:rgba(0,0,0,.3);
    width:100%;
    height:100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index:999;
}
.high-z-index{
    z-index: 9999;
}
.inner-content{
    padding:0 68rpx;
}
.input-box{
    background-color: #eee;
    border-radius: 30rpx;
    width: max-content;
    height: 60rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
    margin: 54rpx 0 0 0;
    padding:0 0 0 24rpx;
    z-index:9999;
    .myInput{
        flex:1;
    }
}
</style>
