<script setup lang="ts">
import { ref } from 'vue'
import Comment from './comment.vue'
import HeaderImgArr from './headerImg-arr.vue'
import LikeIcon from './likeIcon.vue'

const current = ref<number>(0)
const showEidt = ref(false)
const swiperList = ref([
  { url: 'https://xinliushijie.guest.geekdanceshop.com/upload/common/images/20250705/20250705114213175168693367482.JPG', icon: false },
  { url: 'https://xinliushijie.guest.geekdanceshop.com/upload/common/images/20250705/20250705114213175168693367482.JPG', icon: true },
])
// const handleClose = () => {
//     console.log('关闭')
// }
const commentVisable = ref(false)
function eidt() {
  showEidt.value = true
  console.log('当前点击编辑')
}
function handleClick(e) {

}
function onChange(e) {

}
function openComment() {
  commentVisable.value = true
}
// 关闭弹窗
function closeComment() {
  commentVisable.value = false
}
// 状态变量（带类型推断）
const activeTab = ref('about')
</script>

<template>
  <view class="post-card">
    <!-- 可见人员 -->
    <view class="publisher flex-row-item justify-between">
      <view class="flex-row-item">
        <!-- 多图片叠加效果 -->
        <HeaderImgArr :list="['', '']" />
        <view class="name">
          <view>Krystina</view>
          <view>Tokyo, Japan</view>
        </view>
      </view>
      <view style="width: 100px;text-align: right;" @click.stop="eidt">
        <wd-icon name="ellipsis" size="22px" />
      </view>
      <view v-if="showEidt" class="eidt flex-row-item" @click.stop="showEidt = false">
        <image
          src="../static/baby/Pencil.png"
          mode="scaleToFill"
        />
        <text>Edit</text>
      </view>
    </view>
    <view @click="showEidt = false">
      <wd-swiper
        v-model:current="current"
        class="wdSwiperHome"
        :list="swiperList"
        value-key="url"
        title-key="title"
        custom-text-style="color:#000000"
        :indicator="{ type: 'fraction' }"
        indicator-position="top-right"
        :height="375"
        @click="handleClick"
        @change="onChange"
      >
        <template #icon="{ item }">
          <view v-if="item.icon" class="play-icon">
            <wd-icon name="play" size="36px" color="#888" />
          </view>
        </template>
      </wd-swiper>
    </view>
    <view class="bottom">
      <view class="opration flex-row-item justify-between">
        <view>
          <!-- <image
                    src="../static/baby/Heart.png"
                    mode="scaleToFill"
                /> -->
          <LikeIcon />
          <image
            class="comment-icon"
            src="../static/baby/Comment1.png"
            mode="scaleToFill"
            @click="openComment"
          />
        </view>
        <view class="more flex-row-item">
          <view v-for="(item, index) in swiperList" :key="index" :class="{ active: current == index }" />
        </view>
        <image
          src="../static/baby/Send.png"
          mode="scaleToFill"
        />
      </view>
      <view class="like-total">
        <view class="flex-row-item">
          <HeaderImgArr :width="34" :list="['', '']" />
          <view class="name">
            Liked by Emma and 4 others
          </view>
        </view>
        <view class="comment">
          Krystina The baby played in Tokyo and behaved differently. It was very cute! I'm going to share it with you. Isn't it very cute
        </view>
      </view>
      <view class="date flex-row-item justify-between">
        <view>September 19</view>
        <view class="flex-row-item">
          <text>More</text>
          <image
            src="../static/baby/Vector.png"
            mode="scaleToFill"
          />
        </view>
      </view>
    </view>
    <Comment :visable="commentVisable" @close-comment="closeComment" />
  </view>
</template>

<style lang="scss">
.wdSwiperHome{
    ::v-deep .wd-swiper__track{
        border-radius: 0 !important;
    }
}
.slide-container {
  position: relative;  /* 容器相对定位 */
  width: 100%;
  height: 100%;
}
// .play-icon {
//   position: absolute;  /* 图标绝对定位 */
//   top: 50%;
//   left: 50%;
//   transform: translate(-50%, -50%); /* 居中 */
//   z-index: 10; /* 确保在图片上方 */
// }
</style>

<style lang="scss" scoped>
    .post-card{
        background:$color-white;
        border-bottom: 0.2px solid #D0D5DD;
        .publisher{
            padding:20rpx;
            position: relative;
            margin-top:40rpx;
            .eidt{
                color:#667085;
                font-size:$font-size-md;
                background: rgba(249, 250, 251, 0.75);
                border: 1rpx solid rgba(234, 236, 240, 1);
                border-radius:20rpx 0 20rpx 20rpx;
                padding:20rpx;
                position: absolute;
                bottom: -21px;
                z-index: 99;
                right: 23px;
                image{
                    width:40rpx;
                    height:40rpx;
                    margin-right:10rpx;
                }
            }
        }
        .name{
            text-align: left;
            margin-left:20rpx;
            view:nth-child(1){
                font-family:var(--font-family-title);
                font-size:$font-size-sm;
            }
             view:nth-child(2){
                font-size:$font-size-xs;
                margin-top:4rpx;
            }
        }
        .bottom{
            .opration{
                position: relative;
                padding:24rpx 28rpx;
                image{
                    width: 48rpx;
                    height: 48rpx;
                }
                .comment-icon{
                    margin-left:36rpx;
                }
            }
            .more{
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                view{
                    background:rgba(0, 0, 0, 0.15);
                    width:12rpx;
                    height:12rpx;
                    border-radius:50%;
                    margin-right:8rpx;
                }
                .active{
                    background:$color-primary;
                }
            }
        }
        .like-total{
            padding:40rpx 24rpx;
            .name{
                margin-left:17rpx;
            }
        }
        .comment{
            text-align: left;
            margin-top:6rpx;
        }
        .date{
            padding:0 28rpx 28rpx 24rpx;
            view:nth-child(1){
                color:rgba(0, 0, 0, 0.4);
            }
            image{
                width:24rpx;
                height:24rpx;
            }
        }
    }
    .play-icon{
        width:80rpx;
        height:80rpx;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        border-radius: 50%;
        background-color: #fff;
    }
</style>
