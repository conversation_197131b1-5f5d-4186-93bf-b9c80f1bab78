<script setup>
import { ref } from 'vue'

const list = ref([])
list.value = [
  'Privacy and security',
  'Preserve permanently',
  'Shared by the whole family',
]
</script>

<template>
  <view class="post-container flex-row-item">
    <view class="tip">
      <view v-for="(item, index) in list" :key="index">
        {{ item }}
      </view>
    </view>
    <view class="add flex-row-item justify-center">
      <image
        src="../static/baby/add-post.png"
        mode="scaleToFill"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
    .post-container {
        margin:92rpx 32rpx 0 32rpx;
        background:$color-FF9B00;
        color:$color-white;
        box-shadow: 0px 23.76px 34.73px 0px #FF9B0033;
        border-radius:40rpx;
        font-family:'Prociono';
        position: relative;

        .tip{
            padding:36rpx 32rpx;
            text-align:left;
            view{
                margin-bottom:16rpx;
            }
            view:nth-child(3){
                margin-bottom:0;
            }
            view::before{
                content:'';
                display:inline-block;
                width: 12rpx;
                height: 12rpx;
                border-radius: 50%;
                background: $color-white;
                margin-right: 14rpx;
            }
        }
        .add{
            width: 248rpx;
            height: 248rpx;
            background: $color-white;
            border-radius:50%;
            border: 0.91px solid;
            position: absolute;
            bottom: 14px;
            right: 14px;
            image{
                width:188rpx;
                height:188rpx;
            }
        }
    }
</style>
