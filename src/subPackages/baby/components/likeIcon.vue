<template>
    <text>
        <image
            src="../static/baby/Heart.png"
            mode="scaleToFill"
            :style="{width:size+'rpx',height:size+'rpx'}"
        />
    </text>
</template>
<script setup>
import { defineProps } from 'vue'
const props = defineProps({
   // 展示图片
  isLike: {
    type: Boolean,
    default: false
  },
  size: {
    type: Number,
    default: 48
  }
})
</script>
<style lang="scss" scoped>
// image{
//     width: 48rpx;
//     height: 48rpx;
// }
</style>