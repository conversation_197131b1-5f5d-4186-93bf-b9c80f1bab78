<template>
    <view class="comment-item flex-row-item justify-between">
        <view class="user-info">
            <!-- 用户头像 -->
            <image
                class="avatar"
                src=""
                mode="scaleToFill"
            />
            <view class="user-details">
                <!-- 用户名 -->
                <view class="username">{{ comment.username }}</view>
                <!-- 评论内容 -->
                <view class="comment-content">
                    {{ comment.content }}
                </view>
                <!-- 评论时间 -->
                <view class="timestamp flex-row-item justify-between">
                    <text>{{ comment.timestamp }}</text>
                    <view class="flex-row-item">
                        <text>More</text>
                        <image
                            src="../static/baby/Arrow-down.png"
                            mode="scaleToFill"
                        />
                    </view>
                </view>
            </view>
        </view>
        <!-- 操作区域 -->
        <view class="opreation flex-row-item">
            <view>
                <LikeIcon :size="40"/>
            </view>
            <view>
            <CommentIcon :size="40"/>
            </view>
        </view>
    </view>
</template>
<script setup>
import LikeIcon from './likeIcon.vue'
import CommentIcon from './commentIcon.vue'
import { defineProps } from 'vue'
const props = defineProps({
    // 展示弹窗
    comment: {
        type: Object,
        default: {}
    }
})
</script>
<style lang="scss" scoped>
.comment-item {
    padding: 18px 0;
    border-bottom: 1px solid #f6f6f6;
    .user-info {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
        .avatar{
            width:64rpx;
            height:64rpx;
            border: 2px solid rgba(29, 35, 46, 0.2);
            border-radius:50%;
            background:pink;
            flex-shrink: 0;
        }
    }
    .comment-content {
        font-size: $font-size-base;
        color: #333;
        margin-bottom: 12rpx;
        white-space: pre-line; /* 保留换行符 */
    }
    .user-details {
        margin-left: 12px;
         .username {
            font-size: $font-size-mdx;
            font-weight: 600;
            color: $color-black;
            font-family: var(--font-family-title);
        }
    }

    .timestamp {
        font-size: $font-size-sm;
        color: #999;
        margin-top: 2px;
        image{
            width:24rpx;
            height:24rpx;
        }
    }
    .comment-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 48px; /* 与头像对齐 */
    }

    .like-action {
        display: flex;
        align-items: center;
        color: #666;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .opreation{
        view{
            padding:16rpx;
        }
        view:nth-child(1){
            padding-left:28rpx;
        }
    }
}
</style>