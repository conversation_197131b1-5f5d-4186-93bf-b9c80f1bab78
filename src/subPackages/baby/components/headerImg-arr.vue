<template>
    <view class="imagesList">
        <image
            v-for="(item,index) in list"
            :key="index"
            src=""
            mode="scaleToFill"
            :style="{'z-index': list.length-index,'width':`${width}rpx`,'height':`${width}rpx`}"
        />
    </view>
</template>
<script setup>
import { defineProps, ref } from 'vue';

const props = defineProps({
    list:{
        type:Array,
        default:[]
    },
    width:{
        type:Number,
        default:64
    }
})
</script>
<style lang="scss" scoped>
.imagesList{
    position:relative;
    padding-left:20rpx;
    image{
        width: 64rpx;
        height: 64rpx;
        border: 2px solid rgba(29, 35, 46, 0.2);
        border-radius:50%;
        background:pink;
        margin-left:-20rpx;
    }
}
</style>