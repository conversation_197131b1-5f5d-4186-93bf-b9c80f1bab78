<template>
    <text>
        <image
            class="comment-icon"
            src="../static/baby/Comment1.png"
            mode="scaleToFill"
            :style="{width:size+'rpx',height:size+'rpx'}"
        />
    </text>
</template>
<script setup>
import { defineProps } from 'vue'
const props = defineProps({
   // 展示图片
  isLike: {
    type: Boolean,
    default: false
  },
  size: {
    type: Number,
    default: 48
  }
})
</script>
<style lang="scss" scoped>
// image{
//     width: 48rpx;
//     height: 48rpx;
// }
</style>