<template>
    <view class="function-item flex-row-item justify-between"  @click="change" :style="{'animation-delay':((index+1)/10)+'s'}">
        <view class="function-info">
            <image  class="function-icon" :src="option.emoji"></image>
            <view class="function-title">{{ option.title }}</view>
        </view>
        <view class="flex-row-item">
            <view class="member-count" v-if="option.memberCount">{{ option.memberCount }}</view>
            <view class="arrow">
                <image src="@/subPackages/baby/static/baby/arrow-right.png"></image>
            </view>
        </view>
    </view>
</template>
<script setup>
import { defineProps, defineEmits } from 'vue'
const props = defineProps({
    // 展示弹窗
    option: {
        type: Object,
        default: {}
    },
    index:{
        type: Number,
        default: 0.1
    }
})
const emit = defineEmits(['click']);
const change = () => {
    emit('click')
}
</script>
<style lang="scss" scoped>
 .function-item {
    box-shadow: 5px 4px 5px 0px #9E774D24;
    margin-bottom:40rpx;
    padding:24rpx;
    transition: background-color 0.2s ease;
    border-radius:32rpx;
}
.function-icon {
    width: 96rpx;
    height: 96rpx;
    border-radius: 24rpx;
    background-color:#FFF5E3;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
}
.function-item:last-child {
    border-bottom: none;
}

.function-item:hover {
    background-color: #f9f9f9;
}
.member-count {
    font-size: $font-size-base;
    color: #667085;
}

.arrow {
    color: #999;
    image{
        width: 24rpx;
        height: 24rpx;
        margin-left:12rpx;
    }
}
.function-info {
    display: flex;
    align-items: center;
}

.function-title {
    font-size: $font-size-lg;
    font-family:var(--font-family-title);
}
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
.function-item {
    animation-duration: 0.4s;
    animation-timing-function: ease-out;
    animation-fill-mode: both;
    animation-name: fadeIn;
}

// .function-item:nth-child(1) {
//     animation-delay: 0.1s;
//     animation-name: fadeIn;
// }
</style>