<template>
   <view class="delete-btn" @click="change">{{title}}</view>
</template>
<script setup>
import { defineProps, defineEmits } from 'vue'
const props = defineProps({
    // 展示弹窗
    title: {
        type: String,
        default: ''
    }
})
const emit = defineEmits(['click']);
const change = () => {
    emit('click')
}
</script>
<style lang="scss" scoped>
/* 删除按钮 */
.delete-btn {
    background: linear-gradient(92.74deg, #FF7773 9.43%, #FF4473 96.54%);
    color: $color-white;
    padding:28rpx 0;
    width:calc(100% - 74rpx);
    margin:0 37rpx;
    border-radius: 100rpx;
    text-align:center;
    position:fixed;
    bottom:46px;
}

.delete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.4);
}
</style>