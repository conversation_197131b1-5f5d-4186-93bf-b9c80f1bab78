<template>
      <!-- 宝宝卡片 -->
    <view class="baby-card" @click="addBaby">
      <view class="add-btn flex-row-item">
        <text>Add a baby</text>
        <view class="flex-row-item justify-center"> 
          <image
            src="../static/baby/sex-girl.png"
            mode="scaleToFill"
          />
       </view>
      </view>
      <view class="relationship flex-row-item">
        <image
          class="headImg"
          src="../static/baby/headerImg.png"
          mode="scaleToFill"
        />
       <view>
         <view>Who are you</view>
         <view class="flex-row-item name">
           <image
            class="icon"
            src="../static/baby/Location.png"
            mode="scaleToFill"
           />
           <text>Florida</text>
         </view>
       </view>
      </view>
    </view>
</template>
<script setup>
import {defineEmits} from 'vue'
const emit = defineEmits(['add']);
const addBaby = () =>{
    emit('add')
}
</script>
<style lang="scss" scoped>
.baby-card{
    margin:20rpx 32rpx 0 32rpx;
    border-radius:20rpx;
    height:268rpx;
    padding:0 0 0 8rpx;
    position:relative;
    background:url('../static/baby/baby-card-bg.png') no-repeat;
    background-size:100% 100%;
    box-shadow: 0px 0px 16px 2px rgba(0, 0, 0, 0.05);
    .relationship{
      background: #FFFFFF80;
      backdrop-filter: blur(20rpx);
      border-radius:40rpx;
      padding:24rpx;
      width:max-content;
      position: absolute;
      bottom: 8rpx;
      .headImg{
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        box-shadow: 0px 4px 4px 0px #0000001A;
        margin-right:20rpx;
        flex-shrink: 0;
      }
      .name{
        margin-top:16rpx;
      }
      .icon{
        width: 32rpx;
        height: 32rpx;
        margin-right:8rpx;
      }
    }
    .add-btn{
      background:$color-primary;
      color:$color-white;
      position: absolute;
      right: 0;
      top: 40rpx;
      padding:10rpx 40rpx;
      border-radius:40rpx 0 0 40rpx;
      view{
        width:36rpx;
        height:36rpx;
        background:$color-white;
        border-radius:50%;
        margin-left:20rpx;
        image{
          width:20rpx;
          height:20rpx;
        }
      }
    }
  }
</style>