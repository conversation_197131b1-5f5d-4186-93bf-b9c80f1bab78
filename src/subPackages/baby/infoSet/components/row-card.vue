<template>
    <view class="function-item flex-row-item justify-between"  @click="change" :style="{'animation-delay':((index+1)/10)+'s'}">
        <view class="function-info">
            <view class="function-title">{{ option.title }}</view>
        </view>
        <view v-if="option.type==='sex'" class="flex-row-item sex">
            <image :src="sexValue==='girl' ? girlIcon : girlDisableIcon" @click="changeSex('girl')"></image>
            <image :src="sexValue=='boy' ? boyIcon : boyDisableIcon" @click="changeSex('boy')"></image>
        </view>
        <view v-else class="flex-row-item">
            <view class="member-count" v-if="option.description">{{ option.count || option.description }}</view>
            <view class="arrow">
                <image src="../../static/baby/arrow-right.png"></image>
            </view>
        </view>
    </view>
</template>
<script setup>
import { defineProps, defineEmits, ref } from 'vue'
import girlDisableIcon from '../../static/baby/girl-disable.png'
import boyDisableIcon from '../../static/baby/boy-disable.png'
import girlIcon from '../../static/baby/girl.png'
import boyIcon from '../../static/baby/boy.png'
const sexValue = ref('')
const props = defineProps({
    // 展示弹窗
    option: {
        type: Object,
        default: {}
    },
    index:{
        type: Number,
        default: 0.1
    }
})
const emit = defineEmits(['click']);
const change = () => {
    emit('click')
}
const changeSex = (sex) => {
    sexValue.value = sex
}
</script>
<style lang="scss" scoped>
 .function-item {
    // box-shadow: 5px 4px 5px 0px #9E774D24;
    box-shadow: 10px 20px 46px 0px #9E774D24;
    margin-bottom:40rpx;
    padding:24rpx;
    transition: background-color 0.2s ease;
    border-radius:32rpx;
    color:#121213;
}
.function-item:last-child {
    border-bottom: none;
}

.function-item:hover {
    background-color: #f9f9f9;
}
.member-count {
    font-size: $font-size-base;
    color: #667085;
}

.arrow {
    color: #999;
    image{
        width: 24rpx;
        height: 24rpx;
        margin-left:12rpx;
    }
}
.function-info {
    display: flex;
    align-items: center;
}

.function-title {
    font-size: $font-size-lg;
    font-family:var(--font-family-title);
}
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
.function-item {
    animation-duration: 0.4s;
    animation-timing-function: ease-out;
    animation-fill-mode: both;
    animation-name: fadeIn;
}
.sex{
    image{
        width:48rpx;
        height:48rpx;
        margin-left:40rpx;
    }
}
// .function-item:nth-child(1) {
//     animation-delay: 0.1s;
//     animation-name: fadeIn;
// }
</style>