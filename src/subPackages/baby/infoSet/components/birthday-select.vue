<template>
    <view class="add-baby">
        <view class="mask" @click="submit"></view>
        <wd-floating-panel v-model:height="height" :anchors="[100, 300]"  class="high-z-index" :contentDraggable="false" :safeAreaInsetBottom="true" @heightChange="heightChange">
            <view class="inner-content">
                <wd-datetime-picker-view type="date" v-model="date" label="日期选择"  @change="handleChange" />
            </view>
        </wd-floating-panel>
    </view>
</template>
<script setup>
import { ref } from 'vue' 
import { defineProps, defineEmits } from 'vue'
const date = ref('')
const time = ref('')
const height = ref(300)
const sheetShow = ref(true)
const emit = defineEmits(['change']);
const handleChange = ({value}) => {
    time.value = formatTimestamp(value)
}
const heightChange = ({height}) => {
    if(height<=100){
        submit()
    }
}
const close = () => {
    console.log('close')
}
const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需+1
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}
const submit = () => {
    emit('change',time)
}
</script>
<style lang="scss" scoped>
:v-deep .wd-radio{
    margin-top:20px !important;
}
.mask{
    background:rgba(0,0,0,.3);
    width:100%;
    height:100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index:98;
}
.high-z-index{
    z-index: 9999;
}
.inner-content{
    padding:0 68rpx;
}
</style>