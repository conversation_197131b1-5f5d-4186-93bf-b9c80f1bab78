<script setup>
import { ref } from 'vue'
// import { defineProps, defineEmits } from 'vue'
import Navbar from '@/components/navbar/index.vue'
import AddBaby from '@/subPackages/baby/components/add-baby.vue'
import BirthdaySelet from './components/birthday-select.vue'
import RowCard from './components/row-card.vue'
// 默认的宝宝名字
const babyName = ref('Ella')
// 修改宝宝名字弹窗
const eidtBabyShow = ref(false)
const birthdayShow = ref(false)
const birthday = ref('')
const date = ref('')
const time = ref('')
// 功能选项数据
const options = ref([
  {
    title: 'Birthday',
    description: 'Not filled',
    count: birthday,
  },
  {
    title: 'Gender',
    description: 'Not filled',
    type: 'sex',
  },
  {
    title: 'Height',
    description: 'Not filled',
  },
  {
    title: 'Weight',
    description: 'Not filled',
  },
  {
    title: 'Name',
    description: 'Not filled',
    count: babyName,
  },
])
function birthdayChange({ value }) {
  console.log(value)
  birthday.value = value
  birthdayShow.value = false
}
function selectRow(index) {
  // baby关系选择
  if (index === 0) {
    birthdayShow.value = true
  }
  else if (index === 2) {
    uni.navigateTo({
      url: '/subPackages/baby/setHeight/index?type=Height',
    })
  }
  else if (index === 3) {
    //  uni.navigateTo({
    //    url: '/subPackages/baby/setHeight/index?type=Weight',
    //  })
    uni.chooseLocation({
      success: (res) => {
        console.log('位置信息：', res)
        // res 包含 { name, address, latitude, longitude }
      },
      fail: err => console.error('选点失败', err),
    })
  }
  else if (index === 4) {
    eidtBabyShow.value = true
  }
}

function handleChange({ value }) {
  birthday.value = formatTimestamp(value)
}
function close() {
  console.log('close')
}
function formatTimestamp(timestamp) {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始，需+1
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
// 修改宝宝名字
function eidtBabyName(name) {
  console.log('关闭了')
  eidtBabyShow.value = false
  babyName.value = name
}
</script>

<template>
  <view class="">
    <Navbar title="Baby’s information" />
    <!-- 功能选项区域 -->
    <view class="section">
      <!-- 信息选项 -->
      <view v-for="(option, index) in options" :key="index">
        <RowCard :option="option" :index="index" @click="selectRow(index)" />
      </view>
      <BirthdaySelet v-if="birthdayShow" @change="birthdayChange" />
    </view>
    <!-- 修改宝宝姓名 -->
    <AddBaby :show="eidtBabyShow" title="Edit baby's name" :default-name="babyName" @close="eidtBabyName" />
  </view>
</template>

<style lang="scss" scoped>
.section{
   padding:40rpx 32rpx 0 32rpx;
}
</style>
