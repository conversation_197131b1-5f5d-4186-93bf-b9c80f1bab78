<script setup lang="ts">
import { ref, watch } from 'vue'
import AvatarBox from './components/AvatarBox.vue'
import { useAuthStore } from '@/stores/useAuthStore'

// Ai图片
const isExclusive = ref<boolean>(false)
const exclusiveList = ref<string[]>([])
// 裁剪图片
const src = ref<string>('')
// const avatar = ref<string>('')
const show = ref<boolean>(false)

const authStore = useAuthStore()
const avatar = ref(authStore.userInfo.avatar)
// 监听 authStore.userInfo 变化，自动同步到 avatar
watch(
  () => authStore.userInfo,
  (newVal) => {
    avatar.value = newVal.avatar
  },
  { deep: true },
)

// 选择头像
function handleAvatar(item: string) {
  isExclusive.value = false
  avatar.value = item
}

// 打开相册
function handleEdit() {
  uni.chooseImage({
    count: 1,
    success(res) {
      const tempFilePaths = res.tempFilePaths[0]
      src.value = tempFilePaths
      show.value = true
    },
  })
}
// 取消裁剪
function handleCancel(event: any) {
  console.log('取消', event)
}
// 确认裁剪
function handleConfirm(event: any) {
  const { tempFilePath } = event
  avatar.value = tempFilePath
  isExclusive.value = true
}

// 生成专属图片
function handleGenerate() {
  uni.showLoading({
    title: 'Loading...',
    mask: true,
  })

  setTimeout(() => {
    exclusiveList.value = ['1', '2', '3']
    uni.hideLoading()
  }, 1000)
}

// 保存图片
async function handleSaveClick() {
  uni.showToast({
    title: 'save success',
    icon: 'none',
  })
}
</script>

<template>
  <view class="page-container app-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Profile picture
        </view>
      </template>
      <template #right>
        <view class="right-slot" @click="handleSaveClick">
          Save
        </view>
      </template>
    </TopNav>

    <view class="content">
      <AvatarBox :avatar="avatar">
        <view class="edit" @click="handleEdit" />
      </AvatarBox>

      <wd-button :disabled="!isExclusive" class="buttonGenerate" @click="handleGenerate">
        Generate exclusive
        images
      </wd-button>

      <view class="image-list">
        <!-- Ai图 -->
        <view v-if="exclusiveList.length > 0" class="exclusive">
          <view v-for="item in exclusiveList" :key="item" class="exclusive-item">
            <image
              class="image" :src="`/static/mine/library/Aiimage${item}.png`" mode="aspectFill"
              @click="handleAvatar(`/static/mine/library/Aiimage${item}.png`)"
            />
          </view>
        </view>

        <view v-for="item in 6" :key="item" class="image-item">
          <image
            :src="`/static/mine/library/image${item}.png`" mode="scaleToFill"
            @click="handleAvatar(`/static/mine/library/image${item}.png`)"
          />
        </view>
      </view>
      <wd-img-cropper
        v-model="show"
        :img-src="src"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .right-slot {
    width: 100%;
    text-align: right;
    height: 44rpx;
    font-size: 28rpx;
    padding-right: 20rpx;
  }

  .content {
    flex: 1;
    width: 686rpx;
    overflow-y: auto;
    margin: 0 auto;
    padding-top: 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    .edit {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      background-image: url('/static/mine/picture.png');
      background-size: 100% 100%;
    }

    .buttonGenerate {
      width: 454rpx;
      height: 78rpx;
      background: radial-gradient(77.75% 77.75% at 50% 50%, #FCB879 0%, #F6A057 100%),
        linear-gradient(99.78deg, #FFB173 15.81%, #FF9844 99.79%);
      margin-top: 32rpx;
      font-family: var(--font-family-regular);
      font-weight: $font-weight-regular;
      font-size: $font-size-lg;
      border: 2rpx solid #FFCAA1;
      box-shadow: 1px 6px 20px 0px #F1984980;
    }

    .image-list {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      gap: 19rpx;
      flex: 1;
      overflow: auto;
      margin-top: 32rpx;
      align-content: flex-start;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        height: 0;
        color: transparent;
      }

      scrollbar-width: none;
      /* Firefox */
      -ms-overflow-style: none;
      /* IE 10+ */

      .exclusive {
        width: 100%;
        border-radius: 40rpx;
        padding: 10rpx;
        background-color: #F8852D;
        display: flex;
        gap: 9rpx;

        .exclusive-item {
          width: 216rpx;
          height: 216rpx;
          border-radius: 32rpx;

          image {
            width: 216rpx;
            height: 216rpx;
          }
        }
      }

      .image-item {
        width: 216rpx;
        height: 216rpx;
        border-radius: 50%;

        image {
          width: 216rpx;
          height: 216rpx;
        }
      }
    }
  }
}
</style>
