<script setup lang="ts">
import { ref, watch } from 'vue'
import { loginApi } from '@/api/login'
import { mineApi } from '@/api/mine'
import countryPhoneCodes from '@/static/js/countryPhoneCodes'

const loginForm = ref({
  mobile: '',
  code: '',
  password: '',
  confirmPassword: '',
  passwordStatus: true,
  confirmPasswordStatus: true,
  phoneStatus: true,
  codeStatus: true,
})
const isSeedCode = ref(0)

const searchType = ref<string>('+86')
const menu = countryPhoneCodes

// 选择区号
function handleConfirm({ item }: { item: { content: string } }) {
  searchType.value = item.content
}

// 获取验证码
async function handleGetCode() {
  // 获取验证码逻辑
  if (!loginForm.value.mobile) {
    loginForm.value.phoneStatus = false
    return
  }
  if (isSeedCode.value) {
    return
  }
  try {
    const data = await loginApi.seedCode({
      mobile: loginForm.value.mobile,
      scene: 'SZMM',
    })
    console.log(data)

    isSeedCode.value = 60

    const timer = setInterval(() => {
      isSeedCode.value--
      if (isSeedCode.value <= 0) {
        clearInterval(timer)
        isSeedCode.value = 0
      }
    }, 1000)
  }
  catch (error) {
    console.error(error)
  }
}

// 提交
async function handleSubmit() {
  let flag = false
  // if (!loginForm.value.mobile) {
  //   loginForm.value.phoneStatus = false
  //   flag = true
  // }
  // if (!loginForm.value.code) {
  //   loginForm.value.codeStatus = false
  //   flag = true
  // }
  if (!loginForm.value.password) {
    loginForm.value.passwordStatus = false
    flag = true
  }
  if (!loginForm.value.confirmPassword) {
    loginForm.value.confirmPasswordStatus = false
    flag = true
  }
  if (flag) {
    return
  }

  try {
    await mineApi.changePassword({
      // mobile: loginForm.value.mobile,
      // code: loginForm.value.code,
      password: loginForm.value.password,
      password_confirm: loginForm.value.confirmPassword,
    })
    uni.showToast({
      title: 'Password modification successful',
      icon: 'success',
      duration: 1000,
    })
    setTimeout(() => {
      uni.switchTab({
        url: '/subPackages/mine/index',
      })
    }, 1000)
  }
  catch (error) {
  }
}
// 有值时取消错误状态
// watch([() => loginForm.value.mobile], () => {
//   if (loginForm.value.mobile) {
//     loginForm.value.phoneStatus = true
//   }
// })
// watch([() => loginForm.value.code], () => {
//   if (loginForm.value.code) {
//     loginForm.value.codeStatus = true
//   }
// })
watch([() => loginForm.value.password], () => {
  if (loginForm.value.password) {
    loginForm.value.passwordStatus = true
  }
})
watch([() => loginForm.value.confirmPassword], () => {
  if (loginForm.value.confirmPassword) {
    loginForm.value.confirmPasswordStatus = true
  }
})
</script>

<template>
  <view class="page-container app-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Password modification
        </view>
      </template>
    </TopNav>

    <view class="content">
      <!-- 手机号 -->
      <!-- <LoginInput v-model:value="loginForm.mobile" class="phone-input" placeholder="Please enter your phone unmber"
        type="number" :status="loginForm.phoneStatus">
        <template #preposition>
          <view class="phone-preposition">
            <wd-picker v-model="searchType" :columns="menu" use-default-slot @confirm="handleConfirm">
              <view class="search-type">
                <text class="search-type-text">
                  {{ searchType }}
                </text>
                <wd-icon name="arrow-down" size="14px" />
              </view>
            </wd-picker>
          </view>
        </template>
      </LoginInput> -->
      <!-- 验证码 -->
      <!-- <LoginInput v-model:value="loginForm.code" class="phone-input login-input-phone"
        placeholder="Please enter verification code" tip="The verification code was entered incorrectly" mode="code"
        :status="loginForm.codeStatus">
        <template #getCode>
          <view class="get-code" :class="[{ 'get-code-error': isSeedCode }]" @click="handleGetCode">
            <text v-if="isSeedCode">{{ isSeedCode }}</text>
            <text v-else>Get Code</text>
          </view>
        </template>
        <template #forgotPassword>
          <view class="forgot-password" />
        </template>
      </LoginInput> -->

      <LoginInput
        v-model:value="loginForm.password" class="phone-input" placeholder="Please enter password"
        tip="The password was entered incorrectly" type="password" :status="loginForm.passwordStatus"
      >
        <template #forgotPassword>
          <view class="forgot-password">
            <view>The length should be no less than 8 characters, and it should contain at least one</view>
            <view>digit, one English letter and one special character.</view>
            <view>
              Optional special characters include <text>
                ~ ! @ # $ % ^ &amp; * . ( ) _ + = &lt; &gt; 、 / &#123;
                &#125;
              </text>
            </view>
          </view>
        </template>
      </LoginInput>
      <LoginInput
        v-model:value="loginForm.confirmPassword" placeholder="Please enter password again"
        tip="The password was entered incorrectly" type="password" :status="loginForm.confirmPasswordStatus"
      />
    </view>

    <!-- 按钮 -->
    <LargeButton @click="handleSubmit">
      Completed
    </LargeButton>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .content {
    width: 686rpx;
    margin: 0 auto;
    margin-top: 20rpx;

    .phone-input {
      margin-bottom: 40rpx;
    }

    .phone-preposition {
      min-width: 124rpx;
      border-right: 1px solid #E0E0E0;
      height: 100%;
      font-size: $font-size-md;
      display: flex;
      align-items: center;
      justify-content: center;

      .search-type-text {
        padding: 0 10rpx;
      }
    }

    .get-code {
      width: 194rpx;
      height: 80rpx;
      background: $gradient-color-light;

      color: #fff;
      border-top-right-radius: 24rpx;
      border-bottom-right-radius: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .get-code-error {
      background: #D9D9D9;
    }

    .forgot-password {
      font-family: var(--font-family-light);
      font-weight: $font-weight-regular;
      font-size: $font-size-xxs;
      color: #667085;
      margin-top: 20rpx;

      text {
        color: #000;
      }
    }
  }
}
</style>
