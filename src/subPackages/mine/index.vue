<script setup lang="ts">
import { ref } from 'vue'
import ListItem from './components/ListItem.vue'
import { useAuthStore } from '@/stores/useAuthStore'
import AvatarBox from './components/AvatarBox.vue'
import { useNavBar } from '@/utils/height'

const {
  placeholderHeight,
} = useNavBar()


const authStore = useAuthStore()

// 登出弹窗
const showLogout = ref(false)
// 联系我们
const showContact = ref(false)
// 开启推荐
const checked = ref(false)

// 登陆
function handleLogin() {
  uni.navigateTo({
    url: '/subPackages/login/index',
  })
}

// 编辑
function handleEdit() {
  if (!authStore.isLogin) {
    return uni.showToast({
      title: 'Please log in first',
      icon: 'none',
    })
  }
  uni.navigateTo({
    url: '/subPackages/mine/personInfor',
  })
}

// 登出
function handleLogout() {
  showLogout.value = true
}
// 联系我们
function handleConractUs() {
  showContact.value = true
}

// 登出确认
function handleConfirm() {
  authStore.logout()
  // uni.showToast({
  //   title: '登出成功',
  //   icon: 'none',
  // })
  showLogout.value = false
}

// 拨打
function handleDialup() {
  uni.makePhoneCall({
    phoneNumber: authStore.userInfo.phone,
  })
}
// 复制
function handleCopy() {
  uni.setClipboardData({
    data: '***********',
  })
}

// 设置
function handleSetting() {
  if (!authStore.isLogin) {
    return uni.showToast({
      title: 'Please log in first',
      icon: 'none',
    })
  }
  uni.navigateTo({
    url: '/subPackages/mine/settings',
  })
}

// 跳转订单
function handleToOrder() {
  uni.navigateTo({
    url: '/subPackages/mine/myOrder',
  })
}

// 跳转协议
function handlePrivacy() {
  uni.navigateTo({
    url: '/subPackages/mine/privacy',
  })
}

// 注销账号
function handleCancelAccount() {
  console.log("用户点击了注销");
}
</script>

<template>
  <view class="page-container" :style="{ paddingTop: placeholderHeight + 'px' }">
    <!-- 头像 -->
    <AvatarBox :avatar="authStore.userInfo.avatar" @click="handleEdit">
      <view v-if="authStore.isLogin" class="edit" />
    </AvatarBox>

    <!-- 登陆/注册 -->
    <view v-if="!authStore.isLogin" class="login-signup" @click="handleLogin">
      Login/Signup
    </view>
    <view v-else class="user-info">
      <view class="name">
        {{ authStore.userInfo.nickname }}
      </view>
      <view v-if="authStore.userInfo.Badge && authStore.userInfo.Badge.length" class="badge">
        <view v-for="item in authStore.userInfo.Badge" :key="item" class="badge-item">
          <image :src="item" mode="scaleToFill" />
        </view>
      </view>
      <view v-if="authStore.userInfo.level" class="badge">
        <view v-for="item in authStore.userInfo.level" :key="item" class="badge-item">
          <image src="/static/mine/star.png" mode="scaleToFill" />
        </view>
      </view>

      <view class="percent">
        <view class="percent-item percent-box">
          <view class="percent-progress">
            <view class="percent-progress-bar" :style="{ width: `${authStore.userInfo.percent || 0}%` }" />
          </view>
          <view class="percent-text">
            {{ authStore.userInfo.percent || 0 }}%
          </view>
        </view>
        <view class="percent-item">
          The more perfect it is, the more accurate it will be.
        </view>
      </view>
    </view>

    <!-- 订单/vip -->
    <view class="order-vip">
      <view class="item" @click="handleToOrder">
        <image src="/static/mine/order.png" mode="aspectFill" />
        <text>Order</text>
      </view>
      <view class="line" />
      <view class="item">
        <image src="/static/mine/vip.png" mode="aspectFill" />
        <text>VIP</text>
      </view>
    </view>

    <!-- Setting -->
    <ListItem icon="/static/mine/Setting.png" title="Setting" @click="handleSetting" />
    <!-- ContactUS -->
    <ListItem icon="/static/mine/ContactUS.png" title="ContactUS" @click="handleConractUs" />
    <!-- Privacy -->
    <ListItem icon="/static/mine/Privacy.png" title="Privacy" @click="handlePrivacy" />
    <!-- Personalized content recommendation -->
    <ListItem icon="/static/mine/personalized.png" title="Personalized content recommendation" type="none">
      <wd-switch v-model="checked" active-color="#F8852D" custom-class="switch" />
    </ListItem>
    <!-- Cancel the account -->
    <ListItem icon="/static/mine/cancel.png" title="Cancel the account" @click="handleCancelAccount" />
    <!-- Logout -->
    <ListItem v-if="authStore.isLogin" icon="/static/mine/logout.png" title="Log out" @click="handleLogout" />

    <!-- Logout -->
    <Modal :visible="showLogout" title="Are you sure to log out?" cancel-text="Cancel" confirm-text="Confirm"
      @update:visible="showLogout = $event" @cancel="showLogout = false" @confirm="handleConfirm">
      <view class="logout-box">
        <image src="/src/static/mine/logoutDie.png" mode="scaleToFill" />
      </view>
    </Modal>

    <!-- 联系 -->
    <Modal :visible="showContact" title="Contact US" content="TEL：************" cancel-text="Dial up"
      confirm-text="Copy" @update:visible="showContact = $event" @cancel="handleDialup" @confirm="handleCopy" />
  </view>
</template>

<style scoped lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 40rpx;
  font-family: var(--font-family-title);
  // padding-bottom: 300rpx;

  .edit {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 64rpx;
    height: 64rpx;
    border-radius: 50%;
    background-image: url('/static/mine/edit.png');
    background-size: 100% 100%;
  }

  .login-signup {
    margin-top: 32rpx;
    font-weight: $font-weight-bold;
    font-size: $font-size-heading-base;
    text-align: center;
    text-decoration: underline;
    text-decoration-style: solid;
    background: $gradient-color-title;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .user-info {
    width: 686rpx;
    margin-top: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20rpx;

    .name {
      text-align: center;
      line-height: 48rpx;
      font-size: $font-size-heading-base;
      font-weight: $font-weight-bold;
    }

    .badge {
      height: 48rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow-x: auto;
      overflow-y: hidden;
      gap: 8rpx;

      .badge-item {
        width: 48rpx;
        height: 48rpx;

        image {
          width: 48rpx;
          height: 48rpx;
        }
      }
    }

    .percent {
      margin-top: 12rpx;
      height: 72rpx;
      width: 100%;
      font-family: var(--font-family-light);
      font-weight: $font-weight-light;
      font-size: $font-size-base;
      color: #667085;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      // gap: 20rpx;

      .percent-item {
        height: 30rpx;
        line-height: 30rpx;
        text-align: left;

        .percent-progress {
          width: 620rpx;
          height: 12rpx;
          border-radius: 6rpx;
          background-color: #F3E8DF;
          display: flex;
          align-items: center;
        }

        .percent-text {
          font-family: var(--font-family-title);
          font-weight: $font-weight-regular;
          font-size: $font-size-base;
          text-align: center;
          width: 42rpx;
          color: $color-primary;
          padding-right: 6rpx;
        }
      }

      .percent-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .percent-progress-bar {
          width: 320rpx;
          height: 6rpx;
          margin: 0 3rpx;
          border-radius: 4rpx;
          background-color: $color-primary;
        }
      }
    }

  }

  .order-vip {
    width: 686rpx;
    height: 178rpx;
    justify-content: space-between;
    border-radius: 24rpx;
    background-color: #fafafa;
    margin-top: 40rpx;
    padding: 16rpx 112rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: $font-weight-regular;
    font-size: $font-size-md;
    font-family: var(--font-family-regular);
    color: #616161;

    .line {
      height: 66rpx;
      width: 1rpx;
      background-color: #000;
      transform: scaleX(0.2);
      transform-origin: left;
    }

    .item {
      width: 96rpx;
      height: 146rpx;
      display: flex;
      flex-direction: column;
      align-items: center;

      image {
        width: 96rpx;
        height: 96rpx;
      }

      text {
        display: block;
        height: 34rpx;
        margin-top: 12rpx;
        width: 100%;
        text-align: center;
      }
    }
  }

  .logout-container {
    display: flex;
    align-items: center;
    // padding-top: 404rpx;
    justify-content: center;
    height: 100%;

    .logout-box {
      width: 598rpx;
      height: 666rpx;
      box-sizing: border-box;
      padding: 56rpx 40rpx;
      border-radius: 32rpx;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      image {
        width: 266rpx;
        height: 320rpx;
      }

      .logout-title {
        font-family: var(--font-family-title);
        font-weight: $font-weight-bold;
        font-size: $font-size-heading-sm;
        line-height: 100%;
        height: 44rpx;
        margin-top: 56rpx;
        text-align: center;
      }

      .logout-btn-box {
        width: 518rpx;
        height: 78rpx;
        margin-top: 56rpx;
        display: flex;
        justify-content: space-between;
        font-family: var(--font-family-title);
        font-weight: $font-weight-regular;
        font-size: $font-size-md;
        line-height: 74rpx;
        text-align: center;

        .logout-btn {
          width: 230rpx;
          height: 78rpx;
          border-radius: 40rpx;
          border: 2rpx solid $color-primary;
          box-sizing: border-box;
          background-color: #fff;
          color: $color-primary;
        }

        .logout-btn-confirm {
          background: $gradient-color-light;
          color: #fff;
          border-color: transparent;
        }
      }
    }

    .contact-box {
      height: 352rpx;

      .logout-title {
        margin-top: 0;
      }

      .logout-tip {
        height: 38rpx;
        font-family: var(--font-family-regular);
        font-weight: $font-weight-regular;
        font-size: $font-size-lg;
        text-align: center;
        margin-top: 24rpx;
      }
    }
  }

  .logout-box {
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 266rpx;
      height: 320rpx;
      margin-bottom: 56rpx;
    }
  }

  .switch {
    --wot-switch-circle-size: 40rpx;
    --wot-switch-width: 96rpx;
    --wot-switch-height: 48rpx;
  }
}
</style>
