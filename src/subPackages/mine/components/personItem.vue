<script setup lang="ts">
const props = defineProps<{
  title: string
}>()
</script>

<template>
  <view class="list-item">
    <view class="title">
      {{ props.title }}
    </view>
    <slot />
  </view>
</template>

<style scoped lang="scss">
.list-item {
  display: flex;
  justify-content: space-between;
  background-color: #fafafa;
  height: 112rpx;
  align-items: center;
  box-sizing: border-box;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  margin-bottom: 32rpx;

  .title {
    width: 330rpx;
    font-family: var(--font-family-regular);
    font-weight: $font-weight-regular;
    font-size: $font-size-lg;
    color: #475467;
  }
}
</style>
