<script setup lang="ts">
import PersonItem from './components/personItem.vue';

function handleAccountSecurity() {
  uni.navigateTo({
    url: '/subPackages/mine/accountSecurity',
  })
}
</script>

<template>
  <view class="page-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Settings
        </view>
      </template>
    </TopNav>

    <view class="content">
      <!-- Account security -->
      <PersonItem title="Account security" @click="handleAccountSecurity">
        <view class="input-container">
          <view class="arrow">
            <image src="/static/mine/arrow.png" mode="scaleToFill" />
          </view>
        </view>
      </PersonItem>
      <!-- Clear the cache -->
      <!-- <PersonItem title="Clear the cache">
        <view class="input-container">
          <view class="arrow">
            <image src="/static/mine/arrow.png" mode="scaleToFill" />
          </view>
        </view>
      </PersonItem> -->
      <!-- Current  -->
      <PersonItem title="Current ：1.0.71" />
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .content {
    width: 686rpx;
    margin: 0 auto;
    margin-top: 40rpx;

    .arrow {
      width: 48rpx;
      height: 48rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }
}
</style>
