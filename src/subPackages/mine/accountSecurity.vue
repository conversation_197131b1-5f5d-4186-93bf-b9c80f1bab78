<script setup lang="ts">
import PersonItem from './components/personItem.vue';

function handleBindMobilePhone() {
  uni.navigateTo({
    url: '/subPackages/mine/mobilePhone',
  })
}
function handlePasswordModification() {
  uni.navigateTo({
    url: '/subPackages/mine/passwordModification',
  })
}
</script>

<template>
  <view class="page-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Account security
        </view>
      </template>
    </TopNav>

    <view class="content">
      <!-- Password modification -->
      <PersonItem title="Password modification" @click="handlePasswordModification">
        <view class="input-container">
          <view class="arrow">
            <image src="/static/mine/arrow.png" mode="scaleToFill" />
          </view>
        </view>
      </PersonItem>
      <!-- Bind the mobile phone -->
      <PersonItem title="Bind the mobile phone" @click="handleBindMobilePhone">
        <view class="input-container">
          <view class="arrow">
            <image src="/static/mine/arrow.png" mode="scaleToFill" />
          </view>
        </view>
      </PersonItem>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .content {
    width: 686rpx;
    margin: 0 auto;
    margin-top: 40rpx;

    .arrow {
      width: 48rpx;
      height: 48rpx;

      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }
}
</style>
