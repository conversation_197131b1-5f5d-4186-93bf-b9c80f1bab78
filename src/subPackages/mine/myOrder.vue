<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import ScrollEmpty from '../accompany/components/ScrollEmpty.vue'
import OrderItem from './components/orderItem.vue'

// 每个 tab 的数据状态
interface TabData {
  orderList: any[]
  page: number
  isLastPage: boolean
  isLoading: boolean
  isRefreshing: boolean
}

const tabs = [
  { label: 'Pending payment', id: '1' },
  { label: 'Not dispatched', id: '2' },
  { label: 'Dispatched', id: '3' },
  { label: 'Completed', id: '4' },
  { label: 'Refund in progress', id: '5' },
  { label: 'Refunded', id: '6' },
  { label: 'Rejected', id: '7' },
]
// 当前选中的 tab 索引
const currentIndex = ref(0)
const activeTabId = computed(() => `tab${currentIndex.value}`)

const showContact = ref(false)
const telephone = ref('************')
// 联系我们
function handleConract() {
  showContact.value = true
}

// 拨打
function handleDialup() {
  uni.makePhoneCall({
    phoneNumber: telephone.value,
  })
}
// 复制
function handleCopy() {
  uni.setClipboardData({
    data: telephone.value,
  })
}

// 初始化每个 tab 的数据
const tabData = ref<TabData[]>(
  tabs.map(() => ({
    orderList: [],
    page: 1,
    isLastPage: false,
    isLoading: false,
    isRefreshing: false,
  })),
)

// 切换tab
function switchTab(index: number) {
  if (currentIndex.value === index)
    return

  currentIndex.value = index

  // 如果当前 tab 没有数据，则加载
  if (tabData.value[index].orderList.length === 0) {
    getOrderList(index)
  }
}

// Swiper 滑动事件
function onSwiperChange(e: any) {
  const { current } = e.detail
  currentIndex.value = current

  // 如果当前 tab 没有数据，则加载
  if (tabData.value[current].orderList.length === 0) {
    getOrderList(current)
  }
}

// 获取订单列表
async function getOrderList(tabIndex: number, isRefresh = false) {
  const currentTab = tabData.value[tabIndex]
  if (currentTab.isLoading)
    return

  currentTab.isLoading = true

  try {
    console.log('getOrderList', {
      tabIndex,
      page: currentTab.page,
      status: tabs[tabIndex].id,
      isRefresh,
    })

    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000))
    const mockData: any[] = []

    // 模拟最后一页（第3页后没有数据）
    if (currentTab.page >= 3) {
      currentTab.isLastPage = true
    }

    if (isRefresh || currentTab.page === 1) {
      currentTab.orderList = mockData
    }
    else {
      currentTab.orderList.push(...mockData)
    }

    console.log(`Tab ${tabIndex} 订单数据加载成功`, currentTab.orderList.length)
  }
  catch (error) {
    console.error('获取订单列表失败', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  }
  finally {
    currentTab.isLoading = false
    if (currentTab.isRefreshing) {
      currentTab.isRefreshing = false
    }
  }
}

// 下拉刷新
async function handleRefresh(tabIndex: number) {
  const currentTab = tabData.value[tabIndex]
  currentTab.isRefreshing = true
  currentTab.page = 1
  currentTab.isLastPage = false
  await getOrderList(tabIndex, true)
}

// 上拉分页加载
async function handleScrolltolower(tabIndex: number) {
  const currentTab = tabData.value[tabIndex]
  if (currentTab.isLastPage || currentTab.isLoading) {
    return
  }
  currentTab.page++
  await getOrderList(tabIndex)
}

// 监听tab变化
watch(currentIndex, () => {
  console.log('切换到tab:', currentIndex.value)
})

// 初始化加载第一个tab的数据
getOrderList(0)
</script>

<template>
  <view class="page-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          My order
        </view>
      </template>
    </TopNav>

    <view class="order-container">
      <scroll-view :show-scrollbar="false" scroll-x :scroll-into-view="activeTabId" scroll-with-animation
        class="tabs-scroll">
        <view v-for="(item, index) in tabs" :id="`tab${index}`" :key="index" class="tab-item"
          :class="{ active: currentIndex === index }" @click="switchTab(index)">
          {{ item.label }}
        </view>
      </scroll-view>

      <!-- Swiper 容器 -->
      <swiper class="swiper" :current="currentIndex" :duration="300" @change="onSwiperChange">
        <swiper-item v-for="(tab, tabIndex) in tabs" :key="tabIndex">
          <scroll-view scroll-y class="order-scroll" :show-scrollbar="false" :refresher-enabled="true"
            :refresher-threshold="100" refresher-default-style="white"
            :refresher-triggered="tabData[tabIndex].isRefreshing" refresher-background="transparent"
            @scrolltolower="() => handleScrolltolower(tabIndex)" @refresherrefresh="() => handleRefresh(tabIndex)">
            <!-- 订单列表 -->
            <OrderItem :data="tabData[tabIndex].orderList" @handle-conract="handleConract" />

            <!-- 空状态处理 -->
            <ScrollEmpty :is-loading="tabData[tabIndex].isLoading" :is-last-page="tabData[tabIndex].isLastPage"
              :list="tabData[tabIndex].orderList">
              <image src="/static/mine/orderEmpty.png" class="empty-image" mode="aspectFit" />
              <text class="empty-title">
                The order is empty.
              </text>
              <text class="empty-text">
                You haven't placed any purchase orders yet
              </text>
            </ScrollEmpty>
            <!-- 加载更多提示 -->
            <!-- <view v-if="tabData[tabIndex].isLoading" class="loading-tip">
              <text>Loading...</text>
            </view> -->

            <!-- 没有更多数据提示 -->
            <!-- <view v-if="tabData[tabIndex].isLastPage && tabData[tabIndex].orderList.length > 0" class="no-more-tip">
              <text>No more data</text>
            </view> -->

            <!-- 空状态 -->
            <!-- <view v-if="!tabData[tabIndex].isLoading && tabData[tabIndex].orderList.length === 0" class="empty-state">
              <image src="/static/mine/orderEmpty.png" class="empty-image" mode="aspectFit" />
              <text class="empty-title">
                The order is empty.
              </text>
              <text class="empty-text">
                You haven't placed any purchase orders yet
              </text>
            </view> -->
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
  </view>

  <!-- 联系平台 -->
  <Modal :visible="showContact" title="Platform contact information" :content="telephone" cancel-text="Dial up"
    confirm-text="Copy" @update:visible="showContact = $event" @cancel="handleDialup" @confirm="handleCopy" />
</template>

<style lang="scss">
.page-container {
  height: 100vh;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .order-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;

    .swiper {
      flex: 1;
      width: 100%;

      .order-scroll {
        height: 100%;
        padding: 0 32rpx;
        box-sizing: border-box;
      }

      .loading-tip {
        text-align: center;
        padding: 0 0 40rpx;
        color: #666;
        font-size: 28rpx;
      }

      .no-more-tip {
        text-align: center;
        padding: 0 0 40rpx;
        color: #999;
        font-size: 26rpx;
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 186rpx 0;
        width: 622rpx;
        text-align: center;
        margin: 0 auto;

        .empty-image {
          width: 448rpx;
          height: 366rpx;
          margin-bottom: 40rpx;
        }

        .empty-title {
          font-size: $font-size-heading-ml;
          font-family: var(--font-family-regular);
          font-weight: $font-weight-bold;
          color: #1d2939;
          line-height: 68rpx;
          margin-top: 100rpx;
        }

        .empty-text {
          font-size: $font-size-heading-sm;
          font-family: var(--font-family-regular);
          font-weight: $font-weight-regular;
          color: #98a2b3;
          margin-top: 32rpx;
        }
      }
    }
  }

  .tabs-scroll {
    white-space: nowrap;
    width: 100%;
    padding-left: 32rpx;
    box-sizing: border-box;
    margin: 40rpx 0;
    background-color: #fff;
  }

  .tab-item {
    display: inline-block;
    padding: 22rpx 40rpx;
    margin-right: 24rpx;
    border-radius: 20px;
    background-color: #eee;
    color: #666;
  }

  .tab-item.active {
    background-color: orange;
    color: #fff;
  }
}
</style>
