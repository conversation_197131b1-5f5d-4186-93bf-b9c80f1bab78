<script setup lang="ts">
defineProps({
  id: {
    type: Number,
    default: 0,
  },
  title: {
    type: String,
    default: '',
  },
})
</script>

<template>
  <view class="page-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          {{ title }}
        </view>
      </template>
    </TopNav>

    <view class="content">
      Update date: [Fill in Date]
      Effective date: [Date of filling]
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);
  display: flex;
  flex-direction: column;

  .content {
    width: 686rpx;
    margin: 0 auto;
    margin-top: 40rpx;
  }
}
</style>
