<template>
  <view class="page-container app-container">
    <TopNav>
      <template #middle>
        <view class="middle-slot">
          Invitation
        </view>
      </template>
    </TopNav>

    <view class="content">
      <view class="bady">
        <image src="/static/h5/bady.png" mode="scaleToFill" />
        <view class="title">
          <PERSON><PERSON> invited you to join <PERSON><PERSON><PERSON><PERSON><PERSON>'s growth diary
        </view>
      </view>
      <view class="roleSet">
        <image src="/static/h5/role.png" mode="scaleToFill" />
        <view class="title">I'm <PERSON>'s</view>
        <view class="set-text">Not set<wd-icon name="arrow-right1" size="12px"></wd-icon></view>
      </view>
    </view>

    <!-- 接受 -->
    <view class="button-box">
      <wd-button custom-class="accept" @click="handleAccept">Accept invitation</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
defineProps({
  id: {
    type: String,
    default: ''
  }
});

const handleAccept = () => {
  uni.showLoading({
    title: 'On the way App...',
    mask: true,
  });

  const iosScheme = 'yourapp://invitation'; // 修改为你 App 的实际 scheme
  const androidScheme = 'yourapp://invitation';
  const iosStoreUrl = 'https://apps.apple.com/app/id123456789'; // 修改为你 App 的 App Store 链接
  const androidStoreUrl = 'https://play.google.com/store/apps/details?id=com.example.app'; // 修改为你 App 的 Google Play 链接

  const ua = navigator.userAgent.toLowerCase();
  const isAndroid = ua.indexOf('android') > -1;
  const isIOS = /iphone|ipad|ipod/.test(ua);

  const schemeUrl = isIOS ? iosScheme : androidScheme;
  const fallbackUrl = isIOS ? iosStoreUrl : androidStoreUrl;

  const start = Date.now();

  // 跳转 scheme（唤起 App）
  window.location.href = schemeUrl;

  // 延时判断是否唤起失败
  setTimeout(() => {
    const now = Date.now();
    if (now - start < 1800) {
      // 唤起失败，跳转到商店
      window.location.href = fallbackUrl;
    }
  }, 1500);

  // 最迟关闭 loading（无论跳到哪）
  setTimeout(() => {
    uni.hideLoading();
  }, 3000);
};
</script>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-regular);
  display: flex;
  flex-direction: column;

  .content {
    width: 686rpx;
    margin: 0 auto;
    margin-top: 40rpx;

    .bady {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12rpx 24rpx;

      image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
      }

      .title {
        width: 492rpx;
        height: 76rpx;
        color: #6a6a6a;
        font-size: $font-size-lg;
        font-weight: $font-weight-regular;
      }
    }

    .roleSet {
      height: 144rpx;
      background-color: #fff;
      border-radius: 32rpx;
      box-shadow: 10px 20px 46px 0px #9E774D24;
      margin-top: 40rpx;
      display: flex;
      align-items: center;
      gap: 24rpx;
      padding: 24rpx;
      box-sizing: border-box;

      .title {
        flex: 1;
        font-weight: $font-weight-bold;
        font-size: $font-size-md;
      }

      .set-text {
        height: 30rpx;
        display: flex;
        align-items: center;
        color: #667085;
        font-size: $font-size-sm;
        gap: 8rpx;
      }

      image {
        width: 96rpx;
        height: 96rpx;
        border-radius: 24rpx;
      }
    }
  }

  .button-box {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .accept {
    width: 600rpx;
    height: 100rpx;
    position: absolute;
    bottom: 160rpx;
    background: linear-gradient(92.74deg, #FF7773 9.43%, #FF4473 96.54%);
    box-shadow: 0px 8px 16px 0px #FD665559;
    font-size: $font-size-heading-sm;
    text-shadow: 1px 1px 1px 0px #0000001A;
  }
}
</style>