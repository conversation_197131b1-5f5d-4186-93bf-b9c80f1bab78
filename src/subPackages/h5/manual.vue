<template>
  <view class="page-container" :style="{
    backgroundImage: `url('/static/mine/Square.png')`,
    backgroundColor: color
  }">
    <TopNav>
      <template #middle>
        <view class="middle-slot">
          Gigbib
        </view>
      </template>
    </TopNav>

    <view class="content">
      <view class="product">
        <image :src="data.img" mode="scaleToFill" />
        <view class="product-info">
          <view class="product-name">{{ data.title }}</view>
          <view class="product-price">${{ (data.price).toFixed(2) }}</view>
        </view>
      </view>

      <view class="info">
        <view class="info-title">Product Instruction Manual</view>
        <view class="info-content">{{ data.desc }}</view>
      </view>
    </view>

    <view class="footer" :style="{ backgroundColor: color }">
      <wd-button custom-class="button-gigbib" @click="handleGigbib">Go to Gigbib</wd-button>
    </view>

  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const color = ref('#F8852D')

const data = ref(
  {
    id: 1,
    price: 300,
    title: 'Warm Zipper Hooded Jacket',
    desc: 'Warm Zipper Hooded Jacket is a stylish and comfortable jacket that features a warm zipper and hooded design. It is a great addition to any warm weather outfit.',
    img: 'http://gips2.baidu.com/it/u=3130413488,2430785196&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280'
  }
)

// go to gigbib
function handleGigbib() {
  uni.showLoading({
    title: 'On the way App...',
    mask: true,
  });

  const iosScheme = 'yourapp://invitation'; // 修改为你 App 的实际 scheme
  const androidScheme = 'yourapp://invitation';
  const iosStoreUrl = 'https://apps.apple.com/app/id123456789'; // 修改为你 App 的 App Store 链接
  const androidStoreUrl = 'https://play.google.com/store/apps/details?id=com.example.app'; // 修改为你 App 的 Google Play 链接

  const ua = navigator.userAgent.toLowerCase();
  const isAndroid = ua.indexOf('android') > -1;
  const isIOS = /iphone|ipad|ipod/.test(ua);

  const schemeUrl = isIOS ? iosScheme : androidScheme;
  const fallbackUrl = isIOS ? iosStoreUrl : androidStoreUrl;

  const start = Date.now();

  // 跳转 scheme（唤起 App）
  window.location.href = schemeUrl;

  // 延时判断是否唤起失败
  setTimeout(() => {
    const now = Date.now();
    if (now - start < 1800) {
      // 唤起失败，跳转到商店
      window.location.href = fallbackUrl;
    }
  }, 1500);

  // 最迟关闭 loading（无论跳到哪）
  setTimeout(() => {
    uni.hideLoading();
  }, 3000);
}
</script>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-regular);
  display: flex;
  flex-direction: column;
  align-items: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // background-color: $color-primary;

  .middle-slot {
    color: #fff;
  }

  .content {
    width: 686rpx;
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;

    .product {
      width: 100%;
      height: 204rpx;
      border-radius: 40rpx;
      background-color: #f9fafb;
      margin-bottom: 48rpx;
      padding: 24rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      gap: 28rpx;

      image {
        width: 156rpx;
        height: 156rpx;
        border-radius: 20rpx;
      }

      .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 10rpx 0;
        box-sizing: border-box;
        justify-content: space-between;

        .product-title {
          font-family: var(--font-family-medium);
          font-weight: $font-weight-medium;
          font-size: $font-size-lg;
          color: #001833;
        }

        .product-price {
          font-family: var(--font-family-Regular);
          font-weight: $font-weight-bold;
          font-size: $font-size-heading-base;
          color: $auxiliary-red-light;
          line-height: 48rpx;
        }
      }
    }

    .info {
      // height: 940rpx;
      flex: 1;
      width: 100%;
      overflow: auto;
      background-color: #fff;
      border-radius: 56rpx 56rpx 0 0;
      padding: 40rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;

      .info-title {
        width: 606rpx;
        font-family: var(--font-family-Regular);
        font-weight: $font-weight-bold;
        font-size: $font-size-heading-md;
        color: #212121;
        height: 76rpx;
        line-height: 76rpx;
        padding-bottom: 24rpx;
        border-bottom: 1px solid #eee;
        text-align: center;
      }

      .info-content {
        flex: 1;
        overflow: auto;

        /* 隐藏滚动条 */
        &::-webkit-scrollbar {
          width: 0;
          height: 0;
          color: transparent;
        }

        scrollbar-width: none;
        /* Firefox */
        -ms-overflow-style: none;
        /* IE 10+ */
      }
    }
  }

  .footer {
    width: 100%;
    height: 188rpx;
    display: flex;
    justify-content: center;
    padding-bottom: env(safe-area-inset-bottom);

    .button-gigbib {
      width: 686rpx;
      height: 128rpx;
      background: $gradient-color-light;
      margin-top: 20rpx;
    }
  }
}
</style>