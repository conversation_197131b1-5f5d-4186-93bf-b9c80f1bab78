<script setup lang="ts">
import TopNav from '@/components/login/TopNav.vue'

const systemInfo = uni.getSystemInfoSync()
const statusBarHeight = systemInfo.statusBarHeight || 0
const safeAreaBottom = systemInfo.safeAreaInsets?.bottom || 0
function handleLogin() {
  uni.navigateTo({
    url: '/subPackages/login/index',
  })
}
</script>

<template>
  <view
    class="page-container" :style="{
      height: `calc(100vh - ${statusBarHeight}px - ${safeAreaBottom}px)`,
    }"
  >
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Forgot password
        </view>
      </template>
    </TopNav>

    <view class="login-box">
      <view class="logo-Gigbib">
        <image src="/static/login/Gigbib.png" mode="aspectFit" />
      </view>

      <view class="login-content">
        <image src="/static/login/success.png" mode="aspectFit" />
      </view>

      <view>
        <wd-button class="login-btn login-btn-active" @click="handleLogin">
          Back to login
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: hidden;
  font-family: var(--font-family-light);

  .login-box{
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
    margin: 20rpx auto 0;
    width: 654rpx;

    .logo-Gigbib {
      width: 306rpx;
      height: 240rpx;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .login-content {
      width: 460rpx;
      height: 288rpx;
      margin-top: 120rpx;
      image {
        max-width: 100%;
        max-height: 100%;
      }
    }

  .login-btn {
    width: 600rpx;
    height: 80rpx;
    border-radius: 100rpx;
    color: #fff;
    line-height: 80rpx;
    text-align: center;
    font-family: var(--font-family-title);
    font-size: $font-size-heading-sm;
    background: #D9D9D9;
    opacity: 1;
    box-shadow: 0px 16rpx 32rpx 0px #B6B6B659;
    position: absolute;
    bottom: 120rpx;
    left: 75rpx;
  }
  .login-btn-active {
    background: $gradient-color-light;
    box-shadow: 0px 8px 16px 0px #FDA05559;
  }

  }
}
</style>
