<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import LoginInput from '@/components/login/LoginInput.vue'
import { loginApi } from '@/api/login.js'
import { useAuthStore } from '@/stores/useAuthStore'

const isPassword = ref(false)
const loginForm = ref({
  mobile: '',
  code: '',
  password: '',
  phoneStatus: true,
  codeStatus: true,
  passwordStatus: true,
})
const isCheck = ref(false)
const isAppleLogin = ref(false)
const isSeedCode = ref(0)

// 获取当前平台是否支持苹果登录
onMounted(() => {
  isAppleLogin.value = uni.getSystemInfoSync().platform.toLowerCase() === 'ios'
})

// 第三方登录
function handleAuthLogin(type: 'facebook' | 'google' | 'apple') {
  console.log(type);
  // #ifdef APP-PLUS
  // uni.login({
  //   provider: type,
  //   success: function (loginRes) {
  //     console.log(loginRes, 'loginRes');
  //       // 登录成功
  //       uni.getUserInfo({
  //           provider: type,
  //           success: function(info) {
  //             console.log(info, 'info');
  //             // 获取用户信息成功, info.authResult保存用户信息
  //           }
  //       })
  //   },
  //   fail: function (err) {
  //       // 登录授权失败
  //       // err.code是错误码
  //   }
  // });

  const targetId = type
 
  plus.oauth.getServices((services) => {
    console.log((services), 'services')

    const targetService = services.find(s => s.id === targetId)
    console.log((targetService), 'targetService')
    if (!targetService) {
      uni.showToast({ title: `Service ${type} not available`, icon: 'none' })
      return
    }
    uni.showLoading()

    targetService.login((res) => {
      console.log(`${type} 登录成功`, targetService.authResult, (res))
      uni.hideLoading()
      uni.showToast({ title: `${type} login success`, icon: 'none' })
      // 可以将 authResult 提交后端
    }, (err) => {
      console.error(`${type} 登录失败`, err)
      uni.hideLoading()
      uni.showToast({ title: `${type} login failed`, icon: 'none' })
    })
  }, (err) => {
    console.error('获取登录服务失败', err)
    uni.showToast({ title: 'Login service fetch failed', icon: 'none' })
  })

  // #endif
}

// 切换登录
function handleToggleLogin() {
  // loginForm.value.phone = ''
  loginForm.value.code = ''
  loginForm.value.password = ''
  loginForm.value.phoneStatus = true
  loginForm.value.codeStatus = true
  loginForm.value.passwordStatus = true
  isPassword.value = !isPassword.value
}

// 获取验证码
async function handleGetCode() {
  // 获取验证码逻辑
  if (!loginForm.value.mobile) {
    loginForm.value.phoneStatus = false
    return
  }
  if (isSeedCode.value) {
    // return uni.showToast({ title: `Try again in ${isSeedCode.value} seconds`, icon: 'none' })
    return
  }
  try {
    const data = await loginApi.seedCode({
      mobile: loginForm.value.mobile,
      scene: 'YZMDL'
    })
    console.log(data);
    
    isSeedCode.value = 60
    
    const timer = setInterval(() => {
      isSeedCode.value--
      if (isSeedCode.value <= 0) {
        clearInterval(timer)
        isSeedCode.value = 0
      }
    }, 1000)
  } catch (error) {
    console.error(error)
  }
}

// 忘记密码
function handleForgotPassword() {
  uni.navigateTo({
    url: '/subPackages/login/forgotPassword',
  })
  uni.showToast({ title: 'Forgot password', icon: 'none' })
}

// 登录
function handleLogin() {
  let flag = false
  if (!isCheck.value) {
    flag = true
    return uni.showToast({ title: 'Please agree to the terms and conditions', icon: 'none' })
  }
  if (!loginForm.value.mobile) {
    flag = true
    loginForm.value.phoneStatus = false
  }
  if (!loginForm.value.code && !isPassword.value) {
    flag = true
    loginForm.value.codeStatus = false
  }
  if (isPassword.value && !loginForm.value.password) {
    flag = true
    loginForm.value.passwordStatus = false
  }
  if (flag) {
    return
  }

  // 登录接口
  useAuthStore().handleLoginOnly({ ...loginForm.value, scene: isPassword.value ? 1 : 2, terminal: 1 })
}

// 有值时取消错误状态
watch([() => loginForm.value.mobile], () => {
  if (loginForm.value.mobile) {
    loginForm.value.phoneStatus = true
  }
})
watch([() => loginForm.value.code], () => {
  if (loginForm.value.code) {
    loginForm.value.codeStatus = true
  }
})
watch([() => loginForm.value.password], () => {
  if (loginForm.value.password) {
    loginForm.value.passwordStatus = true
  }
})
</script>

<template>
  <view
    class="page-container app-container">
    <!-- #ifdef APP-PLUS -->
    <view class="login-type" @click="handleToggleLogin">
      <text v-if="isPassword">
        Verification code login
      </text>
      <text v-else>
        Password login
      </text>
    </view>
    <!-- #endif -->
    <view class="login-box">
      <view class="logo-Gigbib">
        <image src="/static/login/Gigbib.png" mode="aspectFit" />
      </view>

      <view class="login-content">
        <!-- 手机号 -->
        <LoginInput
          v-model:value="loginForm.mobile"
          class="login-input-phone"
          title="Enter your phone number"
          placeholder="Please enter phone number"
          tip="The telephone number was entered incorrectly"
          type="number"
          :status="loginForm.phoneStatus"
        />
        <!-- 密码 -->
        <LoginInput
          v-if="isPassword"
          v-model:value="loginForm.password"
          class="login-input-phone"
          title="Enter password"
          placeholder="Please enter password"
          tip="The password was entered incorrectly"
          type="password"
          :status="loginForm.passwordStatus"
        >
          <template #forgotPassword>
            <view class="forgot-password" @click="handleForgotPassword">
              <text>Forgot password?</text>
            </view>
          </template>
        </LoginInput>
        <!-- 验证码 -->
        <LoginInput
          v-else
          v-model:value="loginForm.code"
          class="login-input-phone"
          title="Enter verification code"
          placeholder="Please enter verification code"
          tip="The verification code was entered incorrectly"
          mode="code"
          :status="loginForm.codeStatus"
        >
          <template #getCode>
            <view class="get-code" :class="[{ 'get-code-error': isSeedCode }]" @click="handleGetCode">
              <text v-if="isSeedCode">{{ isSeedCode }}</text>
              <text v-else>Get Code</text>
            </view>
          </template>
          <template #forgotPassword>
            <view class="forgot-password" />
          </template>
        </LoginInput>
      </view>
      <wd-button class="login-btn" :class="{ 'login-btn-active': loginForm.phoneStatus && loginForm.codeStatus && loginForm.passwordStatus }" :disabled="!(loginForm.phoneStatus && loginForm.codeStatus && loginForm.passwordStatus)" @click="handleLogin">
        Login
      </wd-button>

      <!-- 登陆协议 -->
      <view class="login-agreement-box" @click="isCheck = !isCheck">
        <view class="check-box" :class="[{ active: isCheck }]">
          <view v-if="isCheck" class="check" />
        </view>
        <view class="login-agreement">
          I have read and agree to the “<text>User Privacy Agreement Terms</text>” and the “<text>Personal Information License</text>”
        </view>
      </view>

      <!-- 快捷登录 -->
      <!-- #ifdef APP-PLUS -->
      <view class="quick-login">
        <view class="quick-login-title">
          <wd-divider color="#EAECF0">
            OR
          </wd-divider>
        </view>
        <view class="quick-login-list">
          <view style="background-image: url('/static/login/facebookLogin.png');" @click="handleAuthLogin('facebook')" />
          <view style="background-image: url('/static/login/googleLogin.png');" @click="handleAuthLogin('google')" />
          <view v-if="isAppleLogin" style="background-image: url('/static/login/appleLogin.png');" @click="handleAuthLogin('apple')" />
        </view>
      </view>
      <!-- #endif -->
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  overflow: auto;
  font-family: var(--font-family-light);
  // #ifndef APP-PLUS
  padding-top: 176rpx;
  // #endif
}
.login-type {
  color: $color-primary;
  font-size: $font-size-md;
  line-height: 44rpx;
  font-family: var(--font-family-light);
  font-weight: $font-weight-regular;
  text-align: right;
  padding: 22rpx 24rpx;
  height: 88rpx;
  box-sizing: border-box;
  // margin-top: 88rpx;
}
.login-box{
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
  margin: 20rpx auto 0;
  width: 654rpx;

  .logo-Gigbib {
    width: 306rpx;
    height: 240rpx;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .login-content {
    width: 100%;
    margin-top: 60rpx;
    padding: 0 27rpx;
    box-sizing: border-box;

    .login-input-phone {
      margin-bottom: 62rpx;
    }

    .get-code {
        width: 194rpx;
        height: 80rpx;
        background: $gradient-color-light;

        color: #fff;
        border-top-right-radius: 24rpx;
        border-bottom-right-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .get-code-error {
      background: #D9D9D9;
    }

  .forgot-password {
    // position: absolute;
    font-family: Inter;
    font-weight: $font-weight-regular;
    font-size: $font-size-xs;
    color: $color-primary;
    height: 24rpx;
    display: flex;
    justify-content: flex-end;
    margin-top: 20rpx;
  }
  }

  .login-btn {
    width: 654rpx;
    height: 80rpx;
    border-radius: 100rpx;
    color: #fff;
    line-height: 80rpx;
    text-align: center;
    background: #D9D9D9;
    opacity: 1;
    box-shadow: 0px 8px 16px 0px #B6B6B659;
  }

  .login-btn-active {
    background: $gradient-color-light;
    box-shadow: 0px 8px 16px 0px #FDA05559;
  }

  .login-agreement-box {
    margin-top: 56rpx;
    display: flex;

    .check-box {
      width: 24rpx;
      height: 24rpx;
      border: 1px solid #8d8e90;
      border-radius: 8rpx;
      margin-right: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .check {
        width: 16rpx;
        height: 16rpx;
        background: #F46200;
        border-radius: 4rpx;
      }
    }

    .active {
      border-color: #f46201;
    }
  }

  .login-agreement {
    font-family: PingFang SC;
    font-weight: $font-weight-regular;
    font-size: $font-size-xs;
    width: 556rpx;
    color: #98999b;

    text {
      color: $color-primary;
      text-decoration: underline;
      text-decoration-style: solid;
    }
  }
}
.quick-login{
  width: 654rpx;
  margin-top: 40rpx;
  .quick-login-title {
    height: 38rpx;
    margin-bottom: 22rpx;
  }

  .quick-login-list {
    display: flex;
    justify-content: space-between;

    view{
      width: 202rpx;
      height: 202rpx;
      background-size: 100% 100%;
    }
  }
}
</style>
