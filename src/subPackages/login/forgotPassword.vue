<script setup lang="ts">
import { ref, watch } from 'vue'
import TopNav from '@/components/login/TopNav.vue'
import countryPhoneCodes from '@/static/js/countryPhoneCodes.ts'
import LoginInput from '../../components/login/LoginInput.vue'
import { loginApi } from '@/api/login.js'

const loginForm = ref({
  mobile: '',
  code: '',
  password: '',
  confirmPassword: '',
  phoneStatus: true,
  codeStatus: true,
  passwordStatus: true,
  confirmPasswordStatus: true,
})
const isNext = ref(false)
const step = ref(1)
const isSeedCode = ref(0)

const searchType = ref<string>('+86')
const menu = countryPhoneCodes

function handleConfirm({ item }: { item: { content: string } }) {
  searchType.value = item.content
}

// 获取验证码
async function handleGetCode() {
  // 获取验证码逻辑
  if (!loginForm.value.mobile) {
    loginForm.value.phoneStatus = false
    return
  }
  if (isSeedCode.value) {
    // return uni.showToast({ title: `Try again in ${isSeedCode.value} seconds`, icon: 'none' })
    return
  }
  try {
    const data = await loginApi.seedCode({
      mobile: loginForm.value.mobile,
      scene: 'ZHDLMM'
    })
    console.log(data);
    
    isSeedCode.value = 60
    
    const timer = setInterval(() => {
      isSeedCode.value--
      if (isSeedCode.value <= 0) {
        clearInterval(timer)
        isSeedCode.value = 0
      }
    }, 1000)
  } catch (error) {
    console.error(error)
  }
}

// 下一步
async function handleNextStep() {
  if (step.value === 1) {
      isNext.value = true
      step.value = 2
  }
  else if (step.value === 2) {
    let flag = false
    if (!loginForm.value.password) {
      flag = true
      loginForm.value.passwordStatus = false
    }
    if (!loginForm.value.confirmPassword) {
      flag = true
      loginForm.value.confirmPasswordStatus = false
    }
    if (flag) {
      return
    }
    try {
      const data = await loginApi.reset({
        mobile: loginForm.value.mobile,
        code: loginForm.value.code,
        password: loginForm.value.password,
        password_confirm: loginForm.value.confirmPassword
      })
      isNext.value = true
      // uni.navigateBack()
      uni.redirectTo({ url: '/subPackages/login/resetSuccess' })
    } catch (error) {
      console.error(error)
    }
  }
}

// 当 phone 和 code 都有值时，启用按钮；否则禁用
watch([() => loginForm.value.mobile, () => loginForm.value.code], () => {
  if (loginForm.value.mobile && loginForm.value.code) {
    isNext.value = true
  }
  else {
    isNext.value = false
  }
})
// 有值时取消错误状态
watch([() => loginForm.value.mobile], () => {
  if (loginForm.value.mobile) {
    loginForm.value.phoneStatus = true
  }
})
watch([() => loginForm.value.code], () => {
  if (loginForm.value.code) {
    loginForm.value.codeStatus = true
  }
})
watch([() => loginForm.value.password], () => {
  if (loginForm.value.password) {
    loginForm.value.passwordStatus = true
  }
})
watch([() => loginForm.value.confirmPassword], () => {
  if (loginForm.value.confirmPassword) {
    loginForm.value.confirmPasswordStatus = true
  }
})
</script>

<template>
  <view class="page-container app-container">
    <TopNav icon="back">
      <template #middle>
        <view class="middle-slot">
          Forgot password
        </view>
      </template>
    </TopNav>

    <view class="login-box">
      <view class="logo-Gigbib">
        <image src="/static/login/Gigbib.png" mode="aspectFit" />
      </view>

      <view class="login-content">
        <view v-if="step === 1">
          <!-- 手机号 -->
          <LoginInput
            v-model:value="loginForm.mobile"
            class="phone-input"
            placeholder="Please enter phone number"
            tip="The telephone number was entered incorrectly"
            type="number"
            :status="loginForm.phoneStatus"
          >
            <template #preposition>
              <view class="phone-preposition">
                <wd-picker v-model="searchType" :columns="menu" use-default-slot @confirm="handleConfirm">
                  <view class="search-type">
                    <text class="search-type-text">
                      {{ searchType }}
                    </text>
                    <wd-icon name="arrow-down" size="14px" />
                  </view>
                </wd-picker>
              </view>
            </template>
          </LoginInput>
          <!-- 验证码 -->
          <LoginInput
            v-model:value="loginForm.code"
            placeholder="Please enter verification code"
            tip="The verification code was entered incorrectly"
            mode="code"
            :status="loginForm.codeStatus"
          >
            <template #getCode>
              <view class="get-code" :class="[{ 'get-code-error': isSeedCode }]" @click="handleGetCode">
                <text v-if="isSeedCode">{{ isSeedCode }}</text>
                <text v-else>Get Code</text>
              </view>
            </template>
          </LoginInput>
        </view>
        <view v-else>
          <LoginInput
            v-model:value="loginForm.password"
            class="phone-input"
            placeholder="Please enter password"
            tip="The password was entered incorrectly"
            type="password"
            :status="loginForm.passwordStatus"
          >
            <template #forgotPassword>
              <view class="forgot-password">
                <view>The length should be no less than 8 characters, and it should contain at least one</view>
                <view>digit, one English letter and one special character.</view>
                <view>
                  Optional special characters include <text>~ ! @ # $ % ^ &amp; * . ( ) _ + = &lt; &gt; 、 / &#123; &#125;</text>
                </view>
              </view>
            </template>
          </LoginInput>
          <LoginInput
            v-model:value="loginForm.confirmPassword"
            placeholder="Please enter password again"
            tip="The password was entered incorrectly"
            type="password"
            :status="loginForm.confirmPasswordStatus"
          />
        </view>
      </view>

      <view>
        <wd-button class="login-btn" :class="{ 'login-btn-active': isNext || step === 2 }" :disabled="!(isNext || step === 2)" @click="handleNextStep">
          Next step
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  overflow: auto;
  font-family: var(--font-family-light);

  .login-box{
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
    margin: 20rpx auto 0;
    width: 654rpx;

    .logo-Gigbib {
      width: 306rpx;
      height: 240rpx;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .login-content {
      width: 100%;
      margin-top: 120rpx;
      padding: 0 27rpx;
      box-sizing: border-box;

      .phone-input {
        margin-bottom: 60rpx;
      }

      .phone-preposition {
        min-width: 124rpx;
        border-right: 1px solid #E0E0E0;
        height: 100%;
        font-size: $font-size-md;
        display: flex;
        align-items: center;
        justify-content: center;

        .search-type-text {
          padding: 0 10rpx;
        }
      }

      .get-code {
        width: 194rpx;
        height: 80rpx;
        background: $gradient-color-light;

        color: #fff;
        border-top-right-radius: 24rpx;
        border-bottom-right-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .get-code-error {
        background: #D9D9D9;
      }

      .forgot-password {
        font-family: var(--font-family-light);
        font-weight: $font-weight-regular;
        font-size: $font-size-xxs;
        color: #667085;
        margin-top: 20rpx;

        text {
          color: #000;
        }
      }
    }

  .login-btn {
    width: 600rpx;
    height: 80rpx;
    border-radius: 100rpx;
    color: #fff;
    line-height: 80rpx;
    text-align: center;
    background: #D9D9D9;
    font-family: var(--font-family-title);
    font-size: $font-size-heading-sm;
    opacity: 1;
    box-shadow: 0px 16rpx 32rpx 0px #B6B6B659;
    position: absolute;
    bottom: 120rpx;
    left: 75rpx;
  }
  .login-btn-active {
    background: $gradient-color-light;
    box-shadow: 0px 8px 16px 0px #FDA05559;
  }

  }
}
</style>
