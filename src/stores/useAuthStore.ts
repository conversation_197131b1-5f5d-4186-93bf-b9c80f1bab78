import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { loginApi } from '@/api/login.js'

export interface IUserInfo {
  id: number
  nickname: string
  avatar: string
  phone: string
  percent: number
  level: number
  Badge: string[]
  sex: 0 | 1 | 2
  birthday: string
}

// 存储键名常量
const STORAGE_KEYS = {
  IS_LOGIN: 'auth_isLogin',
  USER_INFO: 'auth_userInfo',
  TOKEN: 'token'
}

// 默认用户信息
const defaultUserInfo: IUserInfo = {
  id: 0,
  nickname: '<PERSON>',
  avatar: '/static/mine/defultAvatar.png',
  phone: '',
  percent: 17,
  level: 6,
  Badge: [
    '/static/mine/smartBadge.png',
    '/static/mine/fixBadge.png',
    '/static/mine/manBadge.png',
    '/static/mine/learnBadge.png',
  ],
  sex: 1,
  birthday: '',
}

// 从本地存储读取数据的辅助函数
function getStorageData<T>(key: string, defaultValue: T): T {
  try {
    const data = uni.getStorageSync(key)
    return data ? JSON.parse(data) : defaultValue
  } catch (error) {
    console.warn(`读取存储数据失败: ${key}`, error)
    return defaultValue
  }
}

// 保存数据到本地存储的辅助函数
function setStorageData(key: string, value: any): void {
  try {
    uni.setStorageSync(key, JSON.stringify(value))
  } catch (error) {
    console.warn(`保存存储数据失败: ${key}`, error)
  }
}

export const useAuthStore = defineStore('auth', () => {
  // 从本地存储初始化状态
  const isLogin = ref(getStorageData(STORAGE_KEYS.IS_LOGIN, false))
  const userInfo = ref<IUserInfo>(getStorageData(STORAGE_KEYS.USER_INFO, defaultUserInfo))

  // 监听状态变化，自动持久化
  watch(isLogin, (newValue) => {
    setStorageData(STORAGE_KEYS.IS_LOGIN, newValue)
  }, { immediate: false })

  watch(userInfo, (newValue) => {
    setStorageData(STORAGE_KEYS.USER_INFO, newValue)
  }, { deep: true, immediate: false })

  async function handleLoginOnly(form: {
    mobile: string
    code?: string
    password?: string
    scene: 1 | 2 | 3, // 1密码 2验证码 3第三方
    terminal: 1 | 2 | 3 | 4, // 1手机 2facebook 3google 4apple
  }) {
    // 这里可以发请求
    try {
      const data: any  = await loginApi.login({
        mobile: form.mobile,
        code: form.code,
        password: form.password,
        scene: form.scene,
        terminal: form.terminal,
      })
      console.log('登录成功，数据为：', data);
      userInfo.value = data
      isLogin.value = true
      // 保存token
      setStorageData(STORAGE_KEYS.TOKEN, data.token)

      uni.showToast({
        title: 'login success',
        icon: 'none',
      })
      // #ifdef APP-PLUS
      uni.reLaunch({ url: '/pages/index' })
      // #endif
      // #ifdef H5
      uni.reLaunch({ url: '/subPackages/h5/invitation' })
      // #endif
    } catch (error) {
      console.log('error', error);
      
      uni.showToast({
        title: 'login fail',
        icon: 'none',
      })
    }
  }

  // 退出登录
  async function logout() {
    isLogin.value = false
    userInfo.value = { ...defaultUserInfo }

    // 清除本地存储
    try {
      await loginApi.logout()
      uni.removeStorageSync(STORAGE_KEYS.IS_LOGIN)
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO)
      uni.removeStorageSync(STORAGE_KEYS.TOKEN)
      uni.reLaunch({ url: '/subPackages/login/index' })
    } catch (error) {
      console.warn('清除存储数据失败', error)
    }
  }

  // 获取token
  function getToken(): string | null {
    try {
      const token = uni.getStorageSync(STORAGE_KEYS.TOKEN)
      return token ? JSON.parse(token) : null
    } catch (error) {
      console.warn('获取token失败', error)
      return null
    }
  }

  // 检查登录状态
  function checkLoginStatus(): boolean {
    const token = getToken()
    if (!token) {
      logout() // 如果没有token，清除登录状态
      return false
    }
    return isLogin.value
  }

  // 初始化时检查登录状态
  if (isLogin.value) {
    checkLoginStatus()
  }

  return {
    handleLoginOnly,
    logout,
    getToken,
    checkLoginStatus,
    isLogin,
    userInfo,
  }
})
