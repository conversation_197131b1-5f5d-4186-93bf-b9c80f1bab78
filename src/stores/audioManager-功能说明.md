# AudioManager 音频管理器功能说明

## 概述
`audioManager.ts` 是一个基于Pinia的音频管理Store，提供完整的音频播放控制功能。它封装了uni-app的背景音频管理器，提供了更高级的功能和更好的开发体验。

## 核心组件

### 1. 辅助Hook函数

#### `useTrackingPointStore()`
**功能**: 埋点追踪Store，用于统计音频播放数据
- `reportCompletePlay(id)`: 上报完播率，音频播放完成时调用
- `reportConentOpen(id)`: 上报打开率，音频开始播放时调用

#### `useRecordPlayDuration()`
**功能**: 播放时长记录Hook，用于记录用户实际播放时长
- `recordPlayDurationProps`: 播放时长记录属性
- `startRecordPlayDuration()`: 开始记录播放时长
- `stopRecordPlayDuration(complete?)`: 停止记录播放时长

#### `useCoutdown(audioManager)`
**功能**: 倒计时Hook，用于实现定时关闭等功能
- `countdownProps`: 倒计时属性
- `startCountdown()`: 开始倒计时
- `stopCountdown()`: 停止倒计时

#### `useProgress(options)`
**功能**: 进度条控制Hook，处理音频播放进度条的交互逻辑
- `audioProgressIsActiveRef`: 进度条是否处于用户交互状态
- `progressActiveLineWidthRef`: 进度条激活区域宽度（rpx单位）
- `computeProgressLeft()`: 计算进度条左侧位置
- `currentTime2activeLineWidth(time)`: 将当前播放时间转换为进度条宽度
- `progressTouchStartHandler(e)`: 进度条触摸开始事件处理
- `progressTouchMoveHandler(e)`: 进度条触摸移动事件处理
- `progressTouchEndHandler(e)`: 进度条触摸结束事件处理

### 2. 事件常量
```typescript
const AudioManagerEvent = {
  PlayStateChange: 'audio:playStateChange',    // 播放状态改变事件
  CurrentTimeChange: 'audio:currentTimeChange', // 播放时间改变事件
  PlayNextAudio: 'audio:playNextAudio'         // 播放下一首音频事件
}
```

## 主要功能模块

### 1. 音频播放公共属性

#### `audioManagerMeta` (响应式对象)
存储当前播放音频的所有信息：
- `id`: 音频ID，用于标识唯一音频
- `title`: 音频标题
- `coverImgUrl`: 音频封面图片URL
- `epname`: 音频集数名称
- `singer`: 音频演唱者/作者
- `src`: 音频源URL
- `playState`: 播放状态 (true:播放中, false:暂停/停止)
- `isFirstPlay`: 是否首次播放
- `currentTime`: 当前播放时间（秒）
- `totalDurtaion`: 音频总时长（秒）
- `totalDurtaionLabel`: 音频总时长标签（格式化后的时间字符串）

#### `audioSourceDetailRef`
音频源详情引用，存储完整的音频数据

### 2. 音频播放公共方法

#### 数据设置方法
- `setAudioMetaInfo(detail)`: 设置音频元数据信息
- `resetAudioMetaInfo()`: 重置音频元数据信息
- `setAudioManagerInfo()`: 设置uni-app背景音频管理器信息
- `resetAudioManagerInfo()`: 重置uni-app背景音频管理器信息
- `resetAudioManagerAndMetaInfo()`: 重置所有音频相关信息

#### 获取器方法
- `getAudioManagerPlayState()`: 获取音频管理器播放状态
- `getAudioSourceDetail()`: 获取音频源数据详情
- `setAudioSourceDetail(detail)`: 设置音频源数据详情

### 3. 音频管理器操作方法

#### 基础播放控制
- `play()`: 播放音频，如果是首次播放会进行初始化设置
- `pause()`: 暂停音频播放
- `stop()`: 停止音频播放，播放进度会重置到开始位置
- `seek(s)`: 跳转到指定播放位置（秒）

#### 快进/快退功能
- `forward(s)`: 快进功能，将播放位置向前跳跃指定秒数
- `backward(s)`: 快退功能，将播放位置向后跳跃指定秒数

### 4. 播放列表功能

#### 播放列表管理
- `audioPlayListRef`: 音频播放列表引用
- `setAudioManagerPlayList(list)`: 设置音频播放列表
- `playNextAudio()`: 播放下一首音频，支持循环播放

### 5. 进度条相关操作
通过`useProgress` Hook提供的功能：
- 进度条显示和交互
- 触摸拖拽支持
- 播放时间到进度条宽度的转换

### 6. 音频加载相关操作
- `audioProgressIsRefreshRef`: 音频进度刷新状态
- `onTimeUpdateIsPauseRef`: 时间更新暂停状态

## 事件监听系统

### 1. uni-app背景音频管理器事件

#### 播放状态事件
- `onCanplay()`: 音频可播放事件，音频加载完成后触发
- `onPlay()`: 音频播放事件，开始播放时触发
- `onPause()`: 音频暂停事件，暂停播放时触发
- `onStop()`: 音频停止事件，停止播放时触发
- `onEnded()`: 音频自然播放结束事件

#### 进度和错误事件
- `onTimeUpdate()`: 音频播放进度更新事件
- `onError()`: 音频播放错误事件
- `onWaiting()`: 音频加载等待事件

#### 系统控制事件（仅iOS）
- `onPrev()`: 系统音乐面板上一曲事件
- `onNext()`: 系统音乐面板下一曲事件

### 2. Vue响应式监听事件
- 监听播放状态改变：发送全局事件通知
- 监听播放时间改变：更新进度条显示并发送事件通知

## 返回的公共接口

### 核心实例和数据
- `audioManager`: uni-app背景音频管理器实例
- `audioSourceDetail`: 当前音频源详情数据
- `audioManagerMeta`: 音频元数据（响应式）
- `audioPlayList`: 音频播放列表

### 状态属性
- `countdownProps`: 倒计时相关属性
- `audioProgressIsActive`: 进度条是否处于交互状态
- `progressActiveLineWidth`: 进度条激活区域宽度
- `audioProgressIsRefresh`: 音频是否正在刷新/加载

### 方法接口
包含所有上述提到的配置方法、播放控制方法、进度条相关方法、获取器方法和定时器相关方法。

## 使用示例

```typescript
import { useAudioStore } from '@/stores/audioManager'

const audioStore = useAudioStore()
const { 
  play, 
  pause, 
  audioManagerMeta, 
  setAudioMetaInfo,
  setAudioManagerInfo 
} = audioStore

// 设置音频信息并播放
const audioDetail = {
  id: 1,
  name: '音频标题',
  master_name: '作者',
  cover: '封面URL',
  audio: '音频URL',
  duration: 180,
  duration_text: '03:00'
}

setAudioMetaInfo(audioDetail)
setAudioManagerInfo()
play()
```

## 技术特点

1. **响应式设计**: 使用Vue 3的响应式系统，UI能实时反映播放状态
2. **事件驱动**: 完整的事件监听和发送机制
3. **模块化设计**: 功能模块清晰分离，易于维护和扩展
4. **错误处理**: 完善的错误处理和状态管理
5. **跨平台兼容**: 基于uni-app，支持多平台部署
6. **性能优化**: 合理的状态更新策略，避免不必要的重渲染
