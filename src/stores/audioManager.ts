import { defineStore } from 'pinia'
import { ref, reactive, watch } from 'vue'

/**
 * 埋点追踪Store - 用于统计音频播放数据
 * @returns 埋点相关方法
 */
function useTrackingPointStore() {
  return {
    /** 上报完播率 - 音频播放完成时调用 */
    reportCompletePlay: (id: number) => console.log('Report complete play:', id),
    /** 上报打开率 - 音频开始播放时调用 */
    reportConentOpen: (id: number) => console.log('Report content open:', id)
  }
}

/**
 * 播放时长记录Hook - 用于记录用户实际播放时长
 * @returns 播放时长记录相关属性和方法
 */
function useRecordPlayDuration() {
  return {
    /** 播放时长记录属性 */
    recordPlayDurationProps: { startTime: 0 },
    /** 开始记录播放时长 */
    startRecordPlayDuration: () => console.log('Start record play duration'),
    /** 停止记录播放时长 */
    stopRecordPlayDuration: (complete?: boolean) => console.log('Stop record play duration', complete)
  }
}

/**
 * 倒计时Hook - 用于实现定时关闭等功能
 * @param audioManager 音频管理器实例
 * @returns 倒计时相关属性和方法
 */
function useCoutdown(audioManager: any) {
  return {
    /** 倒计时属性 */
    countdownProps: { isActive: false },
    /** 开始倒计时 */
    startCountdown: () => console.log('Start countdown'),
    /** 停止倒计时 */
    stopCountdown: () => console.log('Stop countdown')
  }
}

/**
 * 进度条控制Hook - 处理音频播放进度条的交互逻辑
 * @param options 配置选项，包含audioManager、audioManagerMeta等
 * @returns 进度条相关属性和方法
 */
function useProgress(options: any) {
  /** 进度条激活区域宽度（rpx单位） */
  const progressActiveLineWidthRef = ref(0)
  /** 进度条是否处于用户交互状态（拖拽中） */
  const audioProgressIsActiveRef = ref(false)

  return {
    /** 进度条交互状态 */
    audioProgressIsActiveRef,
    /** 进度条激活宽度 */
    progressActiveLineWidthRef,
    /** 计算进度条左侧位置 */
    computeProgressLeft: () => 0,
    /**
     * 将当前播放时间转换为进度条宽度
     * @param time 当前播放时间（秒）
     */
    currentTime2activeLineWidth: (time: number) => {
      const { audioManagerMeta } = options
      if (audioManagerMeta.totalDurtaion > 0) {
        // 计算进度条宽度：(当前时间 / 总时长) * 进度条总宽度
        progressActiveLineWidthRef.value = (time / audioManagerMeta.totalDurtaion) * 300 // 300rpx 为进度条总宽度
      }
    },
    /**
     * 进度条触摸开始事件处理
     * @param e 触摸事件对象
     */
    progressTouchStartHandler: (e: any) => {
      audioProgressIsActiveRef.value = true
    },
    /**
     * 进度条触摸移动事件处理
     * @param e 触摸事件对象
     */
    progressTouchMoveHandler: (e: any) => {
      // 处理进度条拖拽逻辑
    },
    /**
     * 进度条触摸结束事件处理
     * @param e 触摸事件对象
     */
    progressTouchEndHandler: (e: any) => {
      audioProgressIsActiveRef.value = false
    }
  }
}

/**
 * 音频管理器事件常量定义
 * 用于uni.$emit和uni.$on的事件名称统一管理
 */
const AudioManagerEvent = {
  /** 播放状态改变事件 */
  PlayStateChange: 'audio:playStateChange',
  /** 播放时间改变事件 */
  CurrentTimeChange: 'audio:currentTimeChange',
  /** 播放下一首音频事件 */
  PlayNextAudio: 'audio:playNextAudio'
}

/**
 * 音频管理器主要逻辑Hook
 * 提供完整的音频播放控制功能，包括播放控制、进度管理、播放列表等
 * @returns 音频管理器相关的所有属性和方法
 */
function useAudioManager() {
  // 获取埋点追踪方法
  const { reportCompletePlay, reportConentOpen } = useTrackingPointStore()
  // 获取uni-app背景音频管理器实例
  const audioManager = uni.getBackgroundAudioManager()

  /* ==================== 音频播放公共属性 ==================== */

  /**
   * 音频元数据 - 存储当前播放音频的所有信息
   * 使用reactive使其具有响应式特性，便于UI实时更新
   */
  const audioManagerMeta: AudioManagerMeta = reactive({
    /** 音频ID - 用于标识唯一音频 */
    id: 0,
    /** 音频标题 */
    title: 'Like it Doesn’t Hurt ',
    /** 音频封面图片URL */
    coverImgUrl: 'https://picsum.photos/590/590',
    /** 音频集数名称 */
    epname: '',
    /** 音频演唱者/作者 */
    singer: 'Chris shelet',
    /** 音频源URL */
    src: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
    /** 播放状态 - true:播放中, false:暂停/停止 */
    playState: false,
    /** 是否首次播放 - 用于控制首次播放的特殊逻辑 */
    isFirstPlay: true,
    /** 当前播放时间（秒） */
    currentTime: 0,
    /** 音频总时长（秒） */
    totalDurtaion: 372,
    /** 音频总时长标签（格式化后的时间字符串，如"03:45"） */
    totalDurtaionLabel: '06:12',
  })

  /**
   * 音频源详情引用 - 存储完整的音频数据
   * 包含音频的所有详细信息，如ID、名称、作者、封面、音频URL等
   */
  const audioSourceDetailRef = ref<AudioSourceDetail | null>(null)

  /* ==================== 音频播放公共属性 END ==================== */

  /* ==================== 音频播放公共方法 ==================== */

  /**
   * 设置音频元数据信息
   * 将AudioSourceDetail数据映射到audioManagerMeta中
   * @param detail 音频源详情数据
   */
  function setAudioMetaInfo(detail: AudioSourceDetail) {
    audioManagerMeta.totalDurtaion = detail.duration
    audioManagerMeta.totalDurtaionLabel = detail.duration_text
    audioManagerMeta.id = detail.id
    audioManagerMeta.title = detail.name
    audioManagerMeta.coverImgUrl = detail.cover
    audioManagerMeta.epname = detail.name
    audioManagerMeta.singer = detail.master_name
    audioManagerMeta.src = detail.audio
    // eslint-disable-next-line no-console
    console.log('audioManagerMeta -->', audioManagerMeta)
  }

  /**
   * 重置音频元数据信息
   * 将所有音频元数据重置为初始状态
   */
  function resetAudioMetaInfo() {
    audioManagerMeta.id = 0
    audioManagerMeta.title = ''
    audioManagerMeta.coverImgUrl = ''
    audioManagerMeta.epname = ''
    audioManagerMeta.singer = ''
    audioManagerMeta.src = ''
    audioManagerMeta.playState = false
    audioManagerMeta.isFirstPlay = true
    audioManagerMeta.currentTime = 0
    audioManagerMeta.totalDurtaion = 0
    audioManagerMeta.totalDurtaionLabel = '00:00'
  }

  /**
   * 设置uni-app背景音频管理器信息
   * 将audioManagerMeta中的数据同步到uni-app的背景音频管理器
   * 这是播放音频前的必要步骤
   */
  function setAudioManagerInfo() {
    // 如果是首次播放或音频源为空，设置起始播放时间
    if (audioManagerMeta.isFirstPlay || !audioManager.src) {
      audioManagerMeta.isFirstPlay = false
      audioManager.startTime = audioManagerMeta.currentTime
    }

    // 设置音频管理器的各项属性
    audioManager.title = audioManagerMeta.title
    audioManager.coverImgUrl = audioManagerMeta.coverImgUrl
    audioManager.epname = audioManagerMeta.epname
    audioManager.singer = audioManagerMeta.singer
    audioManager.src = audioManagerMeta.src
  }

  /**
   * 重置uni-app背景音频管理器信息
   * 清空背景音频管理器中的所有音频信息
   */
  function resetAudioManagerInfo() {
    audioManager.title = ''
    audioManager.coverImgUrl = ''
    audioManager.epname = ''
    audioManager.singer = ''
    audioManager.src = ''
  }

  /**
   * 重置所有音频相关信息
   * 同时重置audioManagerMeta和uni-app背景音频管理器
   */
  function resetAudioManagerAndMetaInfo() {
    resetAudioMetaInfo()
    resetAudioManagerInfo()
  }

  /**
   * 获取音频管理器播放状态
   * @returns 当前播放状态 true:播放中, false:暂停/停止
   */
  function getAudioManagerPlayState() {
    return audioManagerMeta.playState
  }

  /**
   * 获取音频源数据详情
   * @returns 当前音频的完整详情数据
   */
  function getAudioSourceDetail() {
    return audioSourceDetailRef.value
  }

  /**
   * 设置音频源数据详情
   * @param detail 音频源详情数据，可为null表示清空
   */
  function setAudioSourceDetail(detail: AudioSourceDetail | null) {
    audioSourceDetailRef.value = detail
  }

  /* ==================== 音频播放公共方法 END ==================== */

  /* ==================== 音频管理器操作方法 ==================== */

  /**
   * 播放音频
   * 开始播放当前设置的音频，如果是首次播放会进行初始化设置
   */
  function play() {
    // 如果是首次播放或音频源为空，进行初始化设置
    if (audioManagerMeta.isFirstPlay || !audioManager.src) {
      audioManagerMeta.isFirstPlay = false
      audioManager.startTime = audioManagerMeta.currentTime
    }

    // 调用uni-app背景音频管理器的播放方法
    audioManager.play()
  }

  /**
   * 暂停音频播放
   * 暂停当前正在播放的音频，可以通过play()方法恢复播放
   */
  function pause() {
    audioManager.pause()
  }

  /**
   * 停止音频播放
   * 完全停止音频播放，播放进度会重置到开始位置
   */
  function stop() {
    audioManager.stop()
  }

  /**
   * 跳转到指定播放位置
   * @param s 目标播放位置，单位：秒
   */
  function seek(s: number) {
    audioManager.seek(s)
  }

  /* ==================== 音频管理器操作方法 END ==================== */

  /* ==================== 记录播放时长功能 ==================== */
  /**
   * 播放时长记录相关功能
   * 用于统计用户实际播放时长，便于数据分析和用户行为追踪
   */
  const {
    /** 播放时长记录属性 */
    recordPlayDurationProps,
    /** 开始记录播放时长 */
    startRecordPlayDuration,
    /** 停止记录播放时长 */
    stopRecordPlayDuration,
  } = useRecordPlayDuration()
  /* ==================== 记录播放时长功能 END ==================== */

  /* ==================== 音频定时器功能 ==================== */
  /**
   * 音频定时器相关功能
   * 用于实现定时关闭、睡眠模式等功能
   */
  const {
    /** 倒计时属性 */
    countdownProps,
    /** 开始倒计时 */
    startCountdown,
    /** 停止倒计时 */
    stopCountdown,
  } = useCoutdown(audioManager)
  /* ==================== 音频定时器功能 END ==================== */

  /* ==================== 音频播放列表功能 ==================== */
  /**
   * 音频播放列表引用
   * 存储当前播放队列中的所有音频数据
   */
  const audioPlayListRef = ref<AudioSourceDetail[]>([])

  /**
   * 设置音频播放列表
   * @param list 音频列表数据
   */
  function setAudioManagerPlayList(list: AudioSourceDetail[]) {
    audioPlayListRef.value = list
  }

  /**
   * 播放下一首音频
   * 根据当前播放列表自动播放下一首音频
   * 支持循环播放（播放到最后一首后回到第一首）
   */
  function playNextAudio() {
    // 在播放列表中查找当前音频的索引
    const currentIndex = audioPlayListRef.value.findIndex(item => item.id === audioManagerMeta.id)

    if (currentIndex !== -1) {
      // 计算下一首音频的索引（循环播放）
      const nextIndex = (currentIndex + 1) % audioPlayListRef.value.length
      const nextAudio = audioPlayListRef.value[nextIndex]

      // 重置播放状态
      audioManagerMeta.currentTime = 0
      audioManager.src = ''

      // 设置下一首音频的信息
      setAudioSourceDetail(nextAudio)
      setAudioMetaInfo(nextAudio)
      setAudioManagerInfo()

      // 埋点统计：记录音频打开率
      reportConentOpen(nextAudio.id)

      // 发送播放下一首音频事件
      uni.$emit(AudioManagerEvent.PlayNextAudio, {
        item: nextAudio,
      })
    }
  }

  /* ==================== 音频播放列表功能 END ==================== */

  /* ==================== 音频进度条相关操作 ==================== */
  /**
   * 音频进度条控制相关功能
   * 处理进度条的显示、用户交互（拖拽）等逻辑
   */
  const {
    /** 进度条是否处于用户交互状态（拖拽中） */
    audioProgressIsActiveRef,
    /** 进度条激活区域宽度（rpx单位） */
    progressActiveLineWidthRef,
    /** 计算进度条左侧位置 */
    computeProgressLeft,
    /** 将当前播放时间转换为进度条宽度 */
    currentTime2activeLineWidth,
    /** 进度条触摸开始事件处理 */
    progressTouchStartHandler,
    /** 进度条触摸移动事件处理 */
    progressTouchMoveHandler,
    /** 进度条触摸结束事件处理 */
    progressTouchEndHandler,
  } = useProgress({
    audioManager,
    audioManagerMeta,
    play,
    seek,
  })

  /* ==================== 音频进度条相关操作 END ==================== */

  /* ==================== 音频加载相关操作 ==================== */

  /**
   * 音频进度刷新状态
   * 用于标识音频是否正在加载/缓冲中
   */
  const audioProgressIsRefreshRef = ref(false)

  /**
   * 时间更新暂停状态
   * 用于控制onTimeUpdate事件的处理逻辑
   */
  const onTimeUpdateIsPauseRef = ref(false)

  /* ==================== 音频加载相关操作 END ==================== */

  /* ==================== 快进/快退相关操作 ==================== */

  /**
   * 快进功能
   * 将播放位置向前跳跃指定秒数
   * @param s 快进的秒数
   */
  function forward(s: number) {
    // 计算新的播放时间，不能超过音频总时长
    const newTime = Math.min(audioManager.currentTime + s, audioManagerMeta.totalDurtaion)

    // 如果已经到达或超过总时长，则不执行快进
    if (newTime >= audioManagerMeta.totalDurtaion) {
      return
    }

    // 跳转到新位置并更新当前时间
    seek(newTime)
    audioManagerMeta.currentTime = Math.floor(newTime)
  }

  /**
   * 快退功能
   * 将播放位置向后跳跃指定秒数
   * @param s 快退的秒数
   */
  function backward(s: number) {
    // 计算新的播放时间，不能小于0
    let newTime = Math.max(audioManager.currentTime - s, 0)

    // 确保时间不会小于0
    if (newTime < 0) {
      newTime = 0
    }

    // 跳转到新位置并更新当前时间
    seek(newTime)
    audioManagerMeta.currentTime = Math.floor(newTime)
  }

  /* ==================== 快进/快退相关操作 END ==================== */

  /* ==================== 音频管理器事件监听 ==================== */

  /**
   * 音频可播放事件监听
   * 当背景音频进入可以播放状态时触发（但不保证后续能流畅播放）
   * 通常在音频加载完成后触发
   */
  audioManager.onCanplay(() => {
    // 如果音频正在刷新/加载中
    if (audioProgressIsRefreshRef.value) {
      // eslint-disable-next-line no-console
      console.log('【音频加载完成 onCanplay】')
      audioProgressIsRefreshRef.value = false

      // 在非Android应用环境下自动开始播放
      // #ifndef APP-ANDROID
      // eslint-disable-next-line no-console
      console.log('播放 --》')
      play()
      // #endif
    }
  })

  /**
   * 音频播放事件监听
   * 当背景音频开始播放时触发
   */
  audioManager.onPlay(() => {
    // eslint-disable-next-line no-console
    console.log('【音频播放 onPlay】')

    // 更新播放状态
    audioManagerMeta.playState = true
    audioManagerMeta.title = audioManager.title
    audioManagerMeta.coverImgUrl = audioManager.coverImgUrl

    // 开始记录播放时长
    recordPlayDurationProps.startTime = Date.now()
    startRecordPlayDuration()
  })

  /**
   * 音频暂停事件监听
   * 当背景音频暂停播放时触发
   */
  audioManager.onPause(() => {
    // eslint-disable-next-line no-console
    console.log('【音频暂停 onPause】')

    // 更新播放状态
    audioManagerMeta.playState = false
    // 停止记录播放时长
    stopRecordPlayDuration()
  })

  /**
   * 音频停止事件监听
   * 当背景音频停止播放时触发
   */
  audioManager.onStop(() => {
    // eslint-disable-next-line no-console
    console.log('【音频停止 onStop】')

    // 更新播放状态和播放进度
    audioManagerMeta.playState = false
    audioManagerMeta.currentTime = 0
    // 停止记录播放时长（标记为完成）
    stopRecordPlayDuration(true)
  })

  /**
   * 音频自然播放结束事件监听
   * 当背景音频播放完毕时触发（非用户主动停止）
   */
  audioManager.onEnded(() => {
    // eslint-disable-next-line no-console
    console.log('【音频结束 onEnded】')

    // 如果有音频详情数据，发送显示金句卡片事件
    if (audioSourceDetailRef.value) {
      uni.$emit('goldenQuoteCard:show', audioSourceDetailRef.value)
    }

    // 更新播放状态
    audioManagerMeta.playState = false
    // 停止记录播放时长（标记为完成）
    stopRecordPlayDuration(true)

    // 埋点统计：记录完播率
    reportCompletePlay(audioManagerMeta.id)

    // 重置播放进度并播放下一首
    audioManagerMeta.currentTime = 0
    playNextAudio()
  })

  /**
   * 音频播放进度更新事件监听
   * 在音频播放过程中定期触发，用于更新播放进度
   */
  audioManager.onTimeUpdate(() => {
    // 如果音频正在刷新/加载中，标记加载完成
    if (audioProgressIsRefreshRef.value) {
      audioProgressIsRefreshRef.value = false
    }

    // 只有在非进度条交互状态且非暂停状态下才更新播放时间
    // 这样可以避免用户拖拽进度条时的冲突
    if (!audioProgressIsActiveRef.value && !onTimeUpdateIsPauseRef.value) {
      audioManagerMeta.currentTime = Math.floor(audioManager.currentTime)
    }
  })

  /**
   * 系统音乐面板上一曲事件监听（仅iOS）
   * 用户在系统音乐播放面板点击上一曲按钮时触发
   */
  audioManager.onPrev(() => {
    // 可以在这里实现上一曲逻辑
  })

  /**
   * 系统音乐面板下一曲事件监听（仅iOS）
   * 用户在系统音乐播放面板点击下一曲按钮时触发
   */
  audioManager.onNext(() => {
    // 可以在这里实现下一曲逻辑
  })

  /**
   * 音频播放错误事件监听
   * 当背景音频播放发生错误时触发
   */
  audioManager.onError(() => {
    // 可以在这里处理播放错误，如显示错误提示、尝试重新播放等
  })

  /**
   * 音频加载等待事件监听
   * 当音频因为数据不足需要停下来加载时触发
   */
  audioManager.onWaiting(() => {
    // eslint-disable-next-line no-console
    console.log('【音频等待加载 onWaiting】')
    // 标记音频正在加载中
    audioProgressIsRefreshRef.value = true
  })

  /* ==================== 音频管理器事件监听 END ==================== */

  /* ==================== Vue响应式监听事件 ==================== */

  /**
   * 监听播放状态改变
   * 当audioManagerMeta.playState发生变化时，发送全局事件通知
   */
  watch(() => audioManagerMeta.playState, (newValue, oldValue) => {
    uni.$emit(AudioManagerEvent.PlayStateChange, {
      oldValue,
      value: newValue,
    })
  })

  /**
   * 监听播放时间改变
   * 当audioManagerMeta.currentTime发生变化时：
   * 1. 更新进度条显示
   * 2. 发送全局事件通知
   */
  watch(() => audioManagerMeta.currentTime, (newValue, oldValue) => {
    // 只有在非进度条交互状态下才更新进度条显示
    // 避免用户拖拽进度条时的视觉冲突
    if (!audioProgressIsActiveRef.value) {
      currentTime2activeLineWidth(newValue)
    }

    // 发送播放时间改变事件
    uni.$emit(AudioManagerEvent.CurrentTimeChange, {
      value: newValue,
      oldValue,
    })
  })

  /* ==================== Vue响应式监听事件 END ==================== */

  /**
   * 返回音频管理器的所有公共属性和方法
   * 这些属性和方法将被暴露给使用该Store的组件
   */
  return {
    // ========== 核心实例和数据 ==========
    /** uni-app背景音频管理器实例 */
    audioManager,
    /** 当前音频源详情数据 */
    audioSourceDetail: audioSourceDetailRef,
    /** 音频元数据（响应式） */
    audioManagerMeta,
    /** 音频播放列表 */
    audioPlayList: audioPlayListRef,

    // ========== 状态属性 ==========
    /** 倒计时相关属性 */
    countdownProps,
    /** 进度条是否处于交互状态 */
    audioProgressIsActive: audioProgressIsActiveRef,
    /** 进度条激活区域宽度 */
    progressActiveLineWidth: progressActiveLineWidthRef,
    /** 音频是否正在刷新/加载 */
    audioProgressIsRefresh: audioProgressIsRefreshRef,

    // ========== 配置方法 ==========
    /** 设置音频管理器信息 */
    setAudioManagerInfo,
    /** 设置音频元数据信息 */
    setAudioMetaInfo,
    /** 设置音频播放列表 */
    setAudioManagerPlayList,
    /** 重置音频元数据 */
    resetAudioMetaInfo,
    /** 重置音频管理器信息 */
    resetAudioManagerInfo,
    /** 重置所有音频信息 */
    resetAudioManagerAndMetaInfo,

    // ========== 播放控制方法 ==========
    /** 播放音频 */
    play,
    /** 暂停音频 */
    pause,
    /** 停止音频 */
    stop,
    /** 跳转到指定位置 */
    seek,
    /** 快进 */
    forward,
    /** 快退 */
    backward,

    // ========== 进度条相关方法 ==========
    /** 计算进度条左侧位置 */
    computeProgressLeft,
    /** 进度条触摸开始处理 */
    progressTouchStartHandler,
    /** 进度条触摸移动处理 */
    progressTouchMoveHandler,
    /** 进度条触摸结束处理 */
    progressTouchEndHandler,

    // ========== 获取器方法 ==========
    /** 获取播放状态 */
    getAudioManagerPlayState,
    /** 获取音频源详情 */
    getAudioSourceDetail,
    /** 设置音频源详情 */
    setAudioSourceDetail,

    // ========== 定时器相关方法 ==========
    /** 开始倒计时 */
    startCountdown,
    /** 停止倒计时 */
    stopCountdown,
  }
}

/**
 * 音频管理器Pinia Store
 *
 * 这是一个基于Pinia的音频管理Store，提供完整的音频播放控制功能。
 *
 * 主要功能包括：
 * - 音频播放控制（播放、暂停、停止、跳转）
 * - 播放进度管理和进度条交互
 * - 播放列表管理和自动播放下一首
 * - 快进/快退功能
 * - 播放时长统计和埋点上报
 * - 定时器功能（如睡眠模式）
 * - 完整的音频事件监听和状态管理
 *
 * 使用方式：
 * ```typescript
 * import { useAudioStore } from '@/stores/audioManager'
 *
 * const audioStore = useAudioStore()
 * const { play, pause, audioManagerMeta } = audioStore
 * ```
 *
 * @returns 音频管理器的所有属性和方法
 */
export const useAudioStore = defineStore('audio', () => {
  return useAudioManager()
})
