import { defineStore } from 'pinia'
import { ref, reactive, watch } from 'vue'

// 模拟缺失的函数
function useTrackingPointStore() {
  return {
    reportCompletePlay: (id: number) => console.log('Report complete play:', id),
    reportConentOpen: (id: number) => console.log('Report content open:', id)
  }
}

function useRecordPlayDuration() {
  return {
    recordPlayDurationProps: { startTime: 0 },
    startRecordPlayDuration: () => console.log('Start record play duration'),
    stopRecordPlayDuration: (complete?: boolean) => console.log('Stop record play duration', complete)
  }
}

function useCoutdown(audioManager: any) {
  return {
    countdownProps: { isActive: false },
    startCountdown: () => console.log('Start countdown'),
    stopCountdown: () => console.log('Stop countdown')
  }
}

function useProgress(options: any) {
  const progressActiveLineWidthRef = ref(0)
  const audioProgressIsActiveRef = ref(false)

  return {
    audioProgressIsActiveRef,
    progressActiveLineWidthRef,
    computeProgressLeft: () => 0,
    currentTime2activeLineWidth: (time: number) => {
      // 简单的进度条计算
      const { audioManagerMeta } = options
      if (audioManagerMeta.totalDurtaion > 0) {
        progressActiveLineWidthRef.value = (time / audioManagerMeta.totalDurtaion) * 300 // 300rpx 为进度条总宽度
      }
    },
    progressTouchStartHandler: (e: any) => {
      audioProgressIsActiveRef.value = true
    },
    progressTouchMoveHandler: (e: any) => {
      // 处理进度条拖拽
    },
    progressTouchEndHandler: (e: any) => {
      audioProgressIsActiveRef.value = false
    }
  }
}

// 音频管理器事件常量
const AudioManagerEvent = {
  PlayStateChange: 'audio:playStateChange',
  CurrentTimeChange: 'audio:currentTimeChange',
  PlayNextAudio: 'audio:playNextAudio'
}

function useAudioManager() {
  const { reportCompletePlay, reportConentOpen } = useTrackingPointStore()
  const audioManager = uni.getBackgroundAudioManager()

  /* 音频播放公共属性 👇 */

  // 音频元数据
  const audioManagerMeta: AudioManagerMeta = reactive({
    id: 0,
    title: '',
    coverImgUrl: '',
    epname: '',
    singer: '',
    src: '',
    playState: false,
    isFirstPlay: true,
    currentTime: 0,
    totalDurtaion: 0,
    totalDurtaionLabel: '00:00',
  })

  // 音频源详情
  const audioSourceDetailRef = ref<AudioSourceDetail | null>(null)

  /* 音频播放公共属性 👆 */

  /* 音频播放公共方法 👇 */

  /** 设置音频数据信息 */
  function setAudioMetaInfo(detail: AudioSourceDetail) {
    audioManagerMeta.totalDurtaion = detail.duration
    audioManagerMeta.totalDurtaionLabel = detail.duration_text
    audioManagerMeta.id = detail.id
    audioManagerMeta.title = detail.name
    audioManagerMeta.coverImgUrl = detail.cover
    audioManagerMeta.epname = detail.name
    audioManagerMeta.singer = detail.master_name
    audioManagerMeta.src = detail.audio
    // eslint-disable-next-line no-console
    console.log('audioManagerMeta -->', audioManagerMeta)
  }

  function resetAudioMetaInfo() {
    audioManagerMeta.id = 0
    audioManagerMeta.title = ''
    audioManagerMeta.coverImgUrl = ''
    audioManagerMeta.epname = ''
    audioManagerMeta.singer = ''
    audioManagerMeta.src = ''
    audioManagerMeta.playState = false
    audioManagerMeta.isFirstPlay = true
    audioManagerMeta.currentTime = 0
    audioManagerMeta.totalDurtaion = 0
    audioManagerMeta.totalDurtaionLabel = '00:00'
  }

  function setAudioManagerInfo() {
    if (audioManagerMeta.isFirstPlay || !audioManager.src) {
      audioManagerMeta.isFirstPlay = false
      audioManager.startTime = audioManagerMeta.currentTime
    }

    audioManager.title = audioManagerMeta.title
    audioManager.coverImgUrl = audioManagerMeta.coverImgUrl
    audioManager.epname = audioManagerMeta.epname
    audioManager.singer = audioManagerMeta.singer
    audioManager.src = audioManagerMeta.src
  }

  function resetAudioManagerInfo() {
    audioManager.title = ''
    audioManager.coverImgUrl = ''
    audioManager.epname = ''
    audioManager.singer = ''
    audioManager.src = ''
  }

  function resetAudioManagerAndMetaInfo() {
    resetAudioMetaInfo()
    resetAudioManagerInfo()
  }

  /** 获取音频管理器播放状态 */
  function getAudioManagerPlayState() {
    return audioManagerMeta.playState
  }

  /** 获取音频源数据详情 */
  function getAudioSourceDetail() {
    return audioSourceDetailRef.value
  }

  /** 设置音频源数据详情 */
  function setAudioSourceDetail(detail: AudioSourceDetail | null) {
    audioSourceDetailRef.value = detail
  }

  /* 音频播放公共方法 👆 */

  /* 音频管理器操作方法 👇 */

  /** 播放 */
  function play() {
    if (audioManagerMeta.isFirstPlay || !audioManager.src) {
      audioManagerMeta.isFirstPlay = false
      audioManager.startTime = audioManagerMeta.currentTime
    }

    audioManager.play()
  }

  /** 暂停 */
  function pause() {
    audioManager.pause()
  }

  /** 停止 */
  function stop() {
    audioManager.stop()
  }

  /** 跳转到指定位置，单位 s */
  function seek(s: number) {
    audioManager.seek(s)
  }

  /* 音频管理器操作方法 👆 */

  /* 记录播放时长 👇 */
  const {
    recordPlayDurationProps,
    startRecordPlayDuration,
    stopRecordPlayDuration,
  } = useRecordPlayDuration()
  /* 记录播放时长 👆 */

  /* 音频定时器 👇 */
  const {
    countdownProps,
    startCountdown,
    stopCountdown,
  } = useCoutdown(audioManager)
  /* 音频定时器 👆 */

  /* 音频播放列表 👇 */
  const audioPlayListRef = ref<AudioSourceDetail[]>([])

  function setAudioManagerPlayList(list: AudioSourceDetail[]) {
    audioPlayListRef.value = list
  }

  /** 播放下一首音频 */
  function playNextAudio() {
    const currentIndex = audioPlayListRef.value.findIndex(item => item.id === audioManagerMeta.id)
    if (currentIndex !== -1) {
      const nextIndex = (currentIndex + 1) % audioPlayListRef.value.length
      const nextAudio = audioPlayListRef.value[nextIndex]

      audioManagerMeta.currentTime = 0
      audioManager.src = ''
      setAudioSourceDetail(nextAudio)
      setAudioMetaInfo(nextAudio)
      setAudioManagerInfo()

      // 埋点：打开率 👇
      reportConentOpen(nextAudio.id)
      // 埋点：打开率 👆

      uni.$emit(AudioManagerEvent.PlayNextAudio, {
        item: nextAudio,
      })
    }
  }

  /* 音频播放列表 👆 */

  /* 音频进度条相关操作 👇 */
  const {
    audioProgressIsActiveRef,
    progressActiveLineWidthRef,
    computeProgressLeft,
    currentTime2activeLineWidth,
    progressTouchStartHandler,
    progressTouchMoveHandler,
    progressTouchEndHandler,
  } = useProgress({
    audioManager,
    audioManagerMeta,
    play,
    seek,
  })
  // 音频进读条是否抬起

  /* 音频进度条相关操作 👆 */

  /* 音频加载相关操作 👇 */

  // 加载状态
  const audioProgressIsRefreshRef = ref(false)
  const onTimeUpdateIsPauseRef = ref(false)

  /* 音频加载相关操作 👆 */

  /* 快进/快退相关操作 👇 */

  // 快进
  function forward(s: number) {
    const newTime = Math.min(audioManager.currentTime + s, audioManagerMeta.totalDurtaion)

    if (newTime >= audioManagerMeta.totalDurtaion) {
      return
    }

    seek(newTime)
    audioManagerMeta.currentTime = Math.floor(newTime)
  }

  // 快退
  function backward(s: number) {
    let newTime = Math.max(audioManager.currentTime - s, 0)

    if (newTime < 0) {
      newTime = 0
    }

    seek(newTime)
    audioManagerMeta.currentTime = Math.floor(newTime)
  }

  /* 快进/快退相关操作 👆 */

  /* 音频管理器事件 👇 */

  /** 背景音频进入可以播放状态，但不保证后面可以流畅播放 */
  audioManager.onCanplay(() => {
    if (audioProgressIsRefreshRef.value) {
      // eslint-disable-next-line no-console
      console.log('【音频加载完成 onCanplay】')
      audioProgressIsRefreshRef.value = false

      // #ifndef APP-ANDROID
      // eslint-disable-next-line no-console
      console.log('播放 --》')
      play()
      // #endif
    }
  })

  /** 背景音频播放事件 */
  audioManager.onPlay(() => {
    // eslint-disable-next-line no-console
    console.log('【音频播放 onPlay】')
    audioManagerMeta.playState = true
    audioManagerMeta.title = audioManager.title
    audioManagerMeta.coverImgUrl = audioManager.coverImgUrl

    recordPlayDurationProps.startTime = Date.now()
    startRecordPlayDuration()
  })

  /** 背景音频暂停事件 */
  audioManager.onPause(() => {
    // eslint-disable-next-line no-console
    console.log('【音频暂停 onPause】')

    audioManagerMeta.playState = false
    stopRecordPlayDuration()
  })

  /** 背景音频停止事件 */
  audioManager.onStop(() => {
    // eslint-disable-next-line no-console
    console.log('【音频停止 onStop】')

    audioManagerMeta.playState = false
    audioManagerMeta.currentTime = 0
    stopRecordPlayDuration(true)
  })

  /** 背景音频自然播放结束事件 */
  audioManager.onEnded(() => {
    // eslint-disable-next-line no-console
    console.log('【音频结束 onEnded】')

    if (audioSourceDetailRef.value) {
      uni.$emit('goldenQuoteCard:show', audioSourceDetailRef.value)
    }

    audioManagerMeta.playState = false
    stopRecordPlayDuration(true)

    // 埋点：完播率 👇
    reportCompletePlay(audioManagerMeta.id)
    // 埋点：完播率 👆

    audioManagerMeta.currentTime = 0
    playNextAudio()
  })

  /** 背景音频播放进度更新事件 */
  audioManager.onTimeUpdate(() => {
    if (audioProgressIsRefreshRef.value) {
      // 加载完成
      audioProgressIsRefreshRef.value = false
    }

    // 必须非进度条抬起状态。播放时才会根据时间转换进度条
    if (!audioProgressIsActiveRef.value && !onTimeUpdateIsPauseRef.value) {
      audioManagerMeta.currentTime = Math.floor(audioManager.currentTime)
    }
  })

  /** 用户在系统音乐播放面板点击上一曲事件（iOS only） */
  audioManager.onPrev(() => {

  })

  /** 用户在系统音乐播放面板点击下一曲事件（iOS only） */
  audioManager.onNext(() => {

  })

  /** 背景音频播放错误事件 */
  audioManager.onError(() => {

  })

  /** 音频加载中事件，当音频因为数据不足，需要停下来加载时会触发 */
  audioManager.onWaiting(() => {
    // eslint-disable-next-line no-console
    console.log('【音频结束 onWaiting】')
    audioProgressIsRefreshRef.value = true
  })

  /* 音频管理器事件 👆 */

  /* 音频监听事件 👇 */

  // 监听播放状态改变事件
  watch(() => audioManagerMeta.playState, (newValue, oldValue) => {
    uni.$emit(AudioManagerEvent.PlayStateChange, {
      oldValue,
      value: newValue,
    })
  })

  // 监听已播放进度条长度，计算已播放时长（单位：秒/s）
  watch(() => audioManagerMeta.currentTime, (newValue, oldValue) => {
    // 必须非进度条抬起状态
    if (!audioProgressIsActiveRef.value) {
      currentTime2activeLineWidth(newValue)
    }

    uni.$emit(AudioManagerEvent.CurrentTimeChange, {
      value: newValue,
      oldValue,
    })
  })

  /* 音频监听事件 👆 */

  return {
    audioManager,
    audioSourceDetail: audioSourceDetailRef,
    audioManagerMeta,
    audioPlayList: audioPlayListRef,
    countdownProps,
    audioProgressIsActive: audioProgressIsActiveRef,
    progressActiveLineWidth: progressActiveLineWidthRef,
    audioProgressIsRefresh: audioProgressIsRefreshRef,
    setAudioManagerInfo,
    setAudioMetaInfo,
    setAudioManagerPlayList,
    resetAudioMetaInfo,
    resetAudioManagerInfo,
    resetAudioManagerAndMetaInfo,
    play,
    pause,
    stop,
    seek,
    forward,
    backward,
    computeProgressLeft,
    getAudioManagerPlayState,
    getAudioSourceDetail,
    setAudioSourceDetail,
    startCountdown,
    stopCountdown,
    progressTouchStartHandler,
    progressTouchMoveHandler,
    progressTouchEndHandler,
  }
}

export const useAudioStore = defineStore('audio', () => {
  return useAudioManager()
})
