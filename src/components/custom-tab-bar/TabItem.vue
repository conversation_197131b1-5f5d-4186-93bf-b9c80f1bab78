<!-- components/custom-tab-bar/TabItem.vue -->
<script setup>
import { storeToRefs } from 'pinia'
import { computed, ref } from 'vue'
import { useTabStore } from '@/stores/tab' // 状态管理

const props = defineProps({
  item: Object,
  active: Boolean,
})

const emit = defineEmits(['click'])
const tabStore = useTabStore()
const { badgeCount } = storeToRefs(tabStore) // 从Pinia获取红点数量

function handleClick() {
  emit('click', props.item)
}
</script>

<template>
  <view
    class="tab-item"
    :class="{ 'special-item': item.isSpecial }"
    @click="handleClick"
  >
    <!-- 特殊按钮（橙色加号） -->
    <view v-if="item.isSpecial" class="special-button">
      <image src="/static/tabbar/add.png" class="icon" />
      <!-- <image :src="active ? item.selectedIcon : item.icon" class="icon" /> -->
    </view>

    <!-- 常规按钮 -->
    <view v-else class="normal-item">
      <image :src="active ? item.selectedIcon : item.icon" class="icon" />
      <text class="text" :class="[{ active }]">
        {{ item.text }}
      </text>
      <!-- Store模块红点 -->
      <view v-if="item.badge && badgeCount > 0" class="badge">
        {{ badgeCount > 99 ? '99+' : badgeCount }}
      </view>
    </view>
  </view>
</template>

<style scoped>
.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.normal-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.icon {
  width: 48rpx;
  height: 48rpx;
}

.text {
  font-size: 20rpx;
  margin-top: 6rpx;
  color: #E9D5C3;
}

.text.active {
  color: #1D232E; /* 主色调橙色 */
}

/* 特殊按钮样式 */
.special-button {
  width: 72rpx;
  height: 72rpx;
  border-radius: 20rpx;
  /* background: linear-gradient(135deg, #FFB472 0%, #F8852D 100%); */
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #E9D5C3;
}

.special-button .icon {
  width: 32rpx;
  height: 32rpx;
}

/* 红点提示 */
.badge {
  position: absolute;
  top: 2px;
  right: -8px;
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  background: #FF2B2B;
  color: white;
  font-size: 20rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
