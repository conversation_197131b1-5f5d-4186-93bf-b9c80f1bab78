<!-- 按钮组件 可自定义宽度-->
<template>
  <button 
    class="gradient-button"
    :class="{ 'disabled-state': disabled }"
    :disabled="disabled"
    @click="handleClick"
  >
    <slot></slot>
  </button>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click']);

const handleClick = (e: Event) => {
  if (props.disabled) {
    e.preventDefault();
    e.stopPropagation();
    console.log('操作被拦截：按钮处于禁用状态');
    return;
  }
  emit('click', e);
};
</script>

<style lang="scss" scoped>
.gradient-button {
  /* 基础样式 */
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  // max-width: 300px;
  height: 128rpx;
  border-radius: 80rpx;
  border: none;
  cursor: pointer;
  font-family: var(--font-family-title);
  /* 文本样式 */
  color: $color-white;
  font-weight: bold;
  font-size: $font-size-lg;
  letter-spacing: 0.5px;
  
  /* 橙色渐变效果 */
  background: radial-gradient(77.75% 77.75% at 50% 50%, #FCB879 0%, #F6A057 100%),
  linear-gradient(99.78deg, #FFB173 15.81%, #FF9844 99.79%);
  /* background: linear-gradient(135deg, #FFB472 0%, #F8852D 100%);
  box-shadow: 0 4px 12px rgba(248, 133, 45, 0.3); */
  
  /* 过渡效果 */
  transition: all 0.3s ease;
}

/* 激活态悬停效果 */
.gradient-button:not(.disabled-state):hover {
  transform: translateY(-2px);
  // box-shadow: 0 6px 15px rgba(248, 133, 45, 0.4);
}

/* 激活态按下效果 */
.gradient-button:not(.disabled-state):active {
  transform: translateY(1px);
  // box-shadow: 0 2px 6px rgba(248, 133, 45, 0.4);
}

/* 禁用状态样式 */
.gradient-button.disabled-state {
  background: linear-gradient(135deg, #E0E0E0 0%, #CCCCCC 100%);
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.7;
  color: $color-white;
}
</style>