<!-- 底部大按钮组件 -->
<template>
  <view class="tofixed">
    <GradientButton :disabled="disabled" @click="change">
      <slot></slot>
    </GradientButton>
  </view>
</template>

<script setup lang="ts">
import { defineProps } from 'vue';
import GradientButton from '@/components/button/gradientButton.vue'

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click']);

const change = (e: Event) => {
  if (props.disabled) {
    e.preventDefault();
    e.stopPropagation();
    console.log('操作被拦截：按钮处于禁用状态');
    return;
  }
  emit('click', e);
};
</script>

<style lang="scss" scoped>
.tofixed{
  position: fixed;
  // bottom: 100px;
  width: calc(100% - 40px);
  margin: 0 20px;
  bottom: 20px; /* 默认值 */
  bottom: max(20px, env(safe-area-inset-bottom)); /* 优先安全区 */
}
</style>