<script setup>
import { defineEmits, defineProps, ref } from 'vue'

const props = defineProps({
  // 展示弹窗
  title: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['click'])
const value1 = ref(0)
const option = ref([
  { label: 'Choose a baby', value: 0 },
  { label: 'Emma', value: 1 },
  { label: 'Kitty', value: 2 },
])
function handleChange(value) {

}
function handleClickLeft() {
  uni.navigateBack()
}
// function rightOperate() {
//   emit('click')
// }
</script>

<template>
  <wd-navbar :title="title" left-arrow safe-area-inset-top custom-style="background-color: transparent !important;" @click-left="handleClickLeft">
    <template v-if="!title" #title>
      <view class="search-box">
        <wd-drop-menu>
          <wd-drop-menu-item v-model="value1" :icon-size="20" :options="option" @change="handleChange" />
        </wd-drop-menu>
      </view>
    </template>
    <template v-if="title" #title>
      <view style="font-size:16px;">
        {{ title }}
      </view>
    </template>
    <template  #right>
      <slot name="right" />
      <!-- <wd-icon name="add" size="16" color="#1D232E" @click="rightOperate" /> -->
    </template>
  </wd-navbar>
</template>

<style lang="scss" scoped>
::v-deep.wd-drop-item__option{
    &.is-active{
        .wd-drop-item__title{
            color: #000;
            font-weight: bold;
        }
    }
    .wd-icon-check{
        display: none;
    }
    .wd-drop-item__title{
        width: 100%;
        text-align: center;
        color: #888;
    }
}
</style>
