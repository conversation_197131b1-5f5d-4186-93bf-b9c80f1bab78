<template>
  <view class="input-box">
    <slot></slot>
    <wd-input
      v-model="localvalue"
      placeholder="Send message"
      :no-border="true"
      style="background-color: transparent;"
      size="large"
      class="input"
      @blur="handleBlur"
      placeholder-style="color: #667085;"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  value: string
}>()

// 定义组件触发的事件
const emit = defineEmits<{
  (e: 'update:value', value: string): void
  (e: 'enter', value: string): void
}>()

// 创建本地响应式数据，用于绑定输入框
const localvalue = ref(props.value)
watch(() => props.value, (newVal) => {
  localvalue.value = newVal
})

// 监听 localvalue 的变化，并将更新后的值通过事件传递给父组件
watch(localvalue, (val) => {
  emit('update:value', val)
})

// 失焦
const handleBlur = () => {
  emit('enter', localvalue.value)
}
</script>

<style scoped lang="scss">
.input-box {
  width: 686rpx;
  height: 100rpx;
  border-radius: 32rpx;
  box-shadow: 0px 0px 16px 2px #0000000D;
  display: flex;
  align-items: center;
  // padding-left: 32rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  font-family: var(--font-family-regular);

  .input {
    width: 100%;
  }
}
</style>
