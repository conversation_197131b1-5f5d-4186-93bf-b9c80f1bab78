<template>
  <view class="item">
    <view class="image-box">

      <image src="/static/mine/Profile.png" mode="scaleToFill" />
    </view>
    <view>
      <view class="title">{{ item.title }}</view>
      <view class="content">{{ item.content }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';

// 定义对话框项目类型
interface DialogItem {
  id: number | string;
  title: string;
  content: string;
}

const props = defineProps({
  item: {
    type: Object as PropType<DialogItem>,
    required: true,
  },
})
</script>

<style scoped lang="scss">
.item {
  height: 160rpx;
  background-color: #fafafa;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  display: flex;
  padding: 28rpx 46rpx 28rpx 30rpx;
  box-sizing: border-box;
  gap: 32rpx;
  align-items: center;

  .image-box {
    width: 100rpx;
    height: 100rpx;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0px 4px 4px 0px #0000001A;

    image {
      width: 64rpx;
      height: 64rpx;
    }
  }

  .title {
    width: 478rpx;
    height: 32rpx;
    color: #101828;
    line-height: 32rpx;
    font-size: $font-size-md;
    font-weight: $font-weight-regular;
    font-family: var(--font-family-regular);
  }

  .content {
    margin-top: 16rpx;
    width: 478rpx;
    height: 56rpx;
    color: #667085;
    line-height: 28rpx;
    font-size: $font-size-base;
    font-weight: $font-weight-light;
    font-family: var(--font-family-light);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>