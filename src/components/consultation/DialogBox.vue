<template>
	<view class="user-container" v-if="data.role === 'user'">
		<view class="user-box">
			<view class="content">
				{{ data.content }}
			</view>
		</view>
		<view class="time">
			{{ formatTime(data.time) }}
		</view>
	</view>
	<view class="chat-container" v-else>
		<view class="chat-box">
			<view class="content">
				{{ data.content }}
			</view>
		</view>
		<view class="time">
			{{ formatTime(data.time) }}
		</view>
	</view>
</template>

<script setup lang="ts">
defineProps<{
	data: {
		role: string;
		content: string;
		id: number;
		time: number;
	};
}>();

function formatTime(timestamp: number): string {
  const date = new Date(timestamp)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}.${minutes}`
}
</script>

<style scoped lang="scss">
.user-container {
	width: 100%;
	display: flex;
	justify-content: end;
	position: relative;
}
.chat-container {
	position: relative;
	display: flex;
	justify-content: start;
}
.time {
	bottom: 32rpx;
	position: absolute;
	height: 34rpx;
	line-height: 34rpx;
	color: #7a8499;
	font-size: $font-size-sm;
}

.user-box {
	background-color: $color-primary;
	width: 432rpx;
	padding: 24rpx;
	color: #fff;
	box-sizing: border-box;
	border-radius: 24rpx 0 24rpx 24rpx;
	margin-bottom: 82rpx;
	position: relative;
}

.chat-box {
	background-color: #f5f6fa;
	width: 536rpx;
	padding: 24rpx;
	color: #000;
	box-sizing: border-box;
	border-radius: 0 24rpx 24rpx 24rpx;
	margin-bottom: 82rpx;
	position: relative;
}

.content {
	font-family: var(--font-family-light);
	font-weight: $font-weight-regular;
	font-size: $font-size-lg;
	line-height: 48rpx;
}
</style>