<template>
  <view class="empty-container">
    <image src="/static/logo.svg" mode="scaleToFill" />

    <text class="empty-title">Hi! I'm <PERSON><PERSON><PERSON>!</text>
    <text class="empty-content">
      I can help you understand the knowledge of educating children and answer your questions. Please start asking me～
    </text>

    <view class="commonly-ask">
      <view v-for="(item, index) in commonly" @click="handleAsk(index)" :key="item" class="commonly-ask-item active">{{ item }}</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const commonly = ref([
  'Is it bad to bribe my kid with snacks?',
  'How do l stop bedtime battles with my kid?',
  'What if my child never listens the first time?',
  'Why is my toddler always throwing tantrums?'
])

defineProps<{
  modelValue: string
}>()

// 定义组件触发的事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'enter', value: string): void
}>()

const handleAsk = (index: number) => {
  emit('update:modelValue', commonly.value[index])
  emit('enter', commonly.value[index])
}

</script>

<style scoped lang="scss">
.empty-container {
  width: 686rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 120rpx;
  text-align: center;
  flex: 1;
  overflow: auto;

  image {
    width: 400rpx;
    height: 400rpx;
  }

  .empty-title {
    margin-top: 48rpx;
    font-family: var(--font-family-title);
    font-weight: $font-weight-bold;
    font-style: Bold;
    font-size: $font-size-heading-base;
    color: #101828;
  }

  .empty-content {
    width: 602rpx;
    font-family: var(--font-family-light);
    font-weight: $font-weight-light;
    font-size: $font-size-md;
    color: #667085;
    margin-top: 24rpx;
  }

  .commonly-ask {
    margin-top: 122rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .commonly-ask-item {
      width: 656rpx;
      height: 74rpx;
      border-radius: 20rpx;
      box-sizing: border-box;
      border: 1px solid #eaecf0;
      box-shadow: 0px 0px 16px 2px #0000000D;
      line-height: 70rpx;
      background-color: #fafafa;
      font-family: var(--font-family-light);
      font-weight: $font-weight-light;
      font-size: $font-size-md;
      color: #98a2b3;
    }
    .active {
      background-color: #fff;
      color: #475467;
    }
  }
}

.input-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  // padding: 16rpx;
  display: flex;
  justify-content: center;
  background-color: #fff;
  z-index: 999;
}

.safe-area-placeholder {
  height: env(safe-area-inset-bottom);
}
</style>