<script setup lang="ts">
import { defineProps } from 'vue'
import type { Component } from 'vue'

const props = defineProps<{
  icon?: string | Component
  color?: string
}>()
function handleClickLeft() {
  uni.navigateBack()
}
</script>

<template>
  <view class="top_nav">
    <wd-navbar safe-area-inset-top custom-style="background-color: transparent !important;">
      <template #left>
        <view class="separator">
          <wd-icon v-if="props.icon === 'back'" name="thin-arrow-left" size="14px" @click="handleClickLeft()"
            :color="color" />
          <slot name="left" v-else />
        </view>
      </template>
      <template #title>
        <slot name="middle" />
      </template>
      <template #right>
        <view class="separator">
          <slot name="right"></slot>
        </view>
      </template>
    </wd-navbar>
  </view>

</template>

<style lang="scss" scoped>
.top_nav {
  --wot-color-border-light: transparent;
}
</style>
