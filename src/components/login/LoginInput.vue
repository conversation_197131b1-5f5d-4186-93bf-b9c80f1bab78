<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  value: string
  placeholder: string
  title?: string
  mode?: 'normal' | 'code'
  type?: 'text' | 'number' | 'digit' | 'idcard' | 'safe-password' | 'nickname' | 'tel' | 'password'
  tip?: string
  status?: boolean
  getCode?: () => void
}>()

// 定义组件触发的事件
const emit = defineEmits<{
  (e: 'update:value', value: string): void
}>()

// 创建本地响应式数据，用于绑定输入框
const localvalue = ref(props.value)
watch(() => props.value, (newVal) => {
  localvalue.value = newVal
})

// 监听 localvalue 的变化，并将更新后的值通过事件传递给父组件
watch(localvalue, (val) => {
  emit('update:value', val)
})
</script>

<template>
  <view class="login-input-box">
    <text v-if="title">
      {{ title }}
    </text>
    <view class="login-input" :class="{ 'error-border': !status }">
      <slot name="preposition" />
      <view class="input-container">
        <wd-input
          v-model="localvalue"
          :type="type !== 'password' ? type : 'text'"
          :show-password="type === 'password'"
          :placeholder="placeholder"
          :no-border="true"
          style="background-color: transparent;"
        />
      </view>
      <slot name="getCode" />
    </view>
    <slot name="forgotPassword" />
    <!-- <view v-if="tip" class="tip"> -->
    <!-- <text v-if="!status">{{ tip }}</text> -->
    <!-- </view> -->
  </view>
</template>

<style scoped lang="scss">
.login-input-box {
  font-family: var(--font-family-light);

  text {
    font-family: var(--font-family-light);
    font-weight: $font-weight-regular;
    font-size: $font-size-md;
    line-height: 100%;
    height: 34rpx;
    letter-spacing: 0%;
    vertical-align: middle;
    margin-bottom: 20rpx;
  }

  .login-input {
    border: 1px solid #E0E0E0;
    margin-top: 20rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    border-radius: 24rpx;
    justify-content: space-between;

    .input-container{
        flex: 1;
        padding: 0 28rpx;

        --wot-input-bg: transparent;
    }

    .get-code {
        width: 194rpx;
        height: 80rpx;
        background: $gradient-color-light;

        color: #fff;
        border-top-right-radius: 24rpx;
        border-bottom-right-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
  }
  .error-border {
    border: 1px solid $color-accent;
    background: #fff4f3;
  }

  .forgot-password {
    // position: absolute;
    // font-family: Inter;
    font-family: var(--font-family-light);
    font-weight: $font-weight-regular;
    font-size: $font-size-xs;
    color: $color-primary;
    height: 24rpx;
    display: flex;
    justify-content: flex-end;
    margin-top: 20rpx;
  }

  .tip {
    font-weight: $font-weight-regular;
    font-size: $font-size-md;
    line-height: 100%;
    height: 30rpx;
    text-align: center;
    color: $auxiliary-red-light;
    margin-top: 24rpx;
    margin-bottom: 52rpx;
    font-family: var(--font-family-light);
  }
}
</style>
