<!-- 徽章弹窗 -->
<template>
    <view>
        <Modal
        :visible="showModal"
        @update:visible="showModal = $event"
        @cancel="handleCancel"
        title="Gigbib Friendship Protector medal !"
        content="Friendship & Anti Bullying"
        :showButtons="false"
        >
          <slot>
            <view class="badge flex-row-item justify-center">
                <image
                    class="badge-img"
                    :src="imgSrc"
                    mode="scaleToFill"
                />
            </view>
          </slot>
       </Modal>
    </view>
</template>
<script setup lang="ts">
import Modal from './index.vue'
import { defineProps,ref } from 'vue';

const showModal = ref(true)
const props = defineProps({
   // 展示图片
  imgSrc: {
    type: String,
    default: ''
  },
  // 标题
  title: {
    type: String,
    default: null
  }
})
const handleCancel = () => {
  // 执行其他取消操作
}
</script>
<style lang="scss" scoped>
.badge{
    .badge-img{
        width:200rpx;
        height:200rpx;
    }
}
</style>