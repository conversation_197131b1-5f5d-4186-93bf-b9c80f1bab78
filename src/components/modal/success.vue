<!-- 自定义弹窗 -->
<template>
  <div class="modal-wrapper" v-if="showVisible" @click="handleModalClick">
    <div class="modal-container">
      <view class="flex-row-item justify-center">
          <image
              class="success-img"
              src="@/subPackages/baby/static/baby/success.png"
              mode="scaleToFill"
          />
      </view>
      <!-- 标题 -->
      <h2 v-if="title" class="modal-title">{{ title }}</h2>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits,ref } from 'vue';

const props = defineProps({
  // 展示图片
  imgSrc: {
    type: String,
    default: null
  },
  visible: {
    type: Boolean,
    required: false
  },
  // 标题
  title: {
    type: String,
    default: null
  }
});
const showVisible = ref(props.visible);
const emit = defineEmits(['update:visible', 'cancel', 'confirm']);

// 处理确认按钮点击
const handleConfirm = () => {
  emit('update:visible', false);
  emit('confirm');
};

// 处理取消按钮点击
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};

// 处理遮罩层点击
const handleModalClick = () => {
  console.log('当前点击了')
  showVisible.value = false;
  emit('update:visible', false);
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.success-img {
  width:112rpx;
  height:112rpx;
}
/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.modal-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(22, 22, 22, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  animation: fadeIn 0.3s ease;
}

.modal-container {
  background-color: #fff;
  border-radius: 32rpx;
  width: min(90%, 400px);
  padding: 26px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.modal-container.no-shadow {
  box-shadow: none;
}

.modal-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
}

.modal-title {
  font-size:$font-size-heading-sm;
  font-weight: 700;
  text-align: center;
  font-family:var(--font-family-title);
}

.modal-content {
  font-size: $font-size-lg;
  color:rgb(140, 139, 139);
  line-height: 1.5;
  text-align: center;
  margin-bottom: 8px;
}

.modal-buttons {
  display: flex;
  gap: 16px;
}

.modal-button {
  padding: 8rpx 48rpx;
  font-size: $font-size-md;
  font-weight: 600;
  border-radius: 140rpx;
  border: none;
  cursor: pointer;
  flex: 1;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: transparent;
  color: $color-secondary;
  border: 1px solid $color-secondary; 
}

.cancel-button:hover {
  background-color:rgba(255, 241, 230, 1);
  // background: linear-gradient(92.74deg, $color-secondary 0.1%, #FF9844 0.1%);
}

.confirm-button {
  /* background: linear-gradient(135deg, #FFB472 0%, #F8852D 100%); */
  background: linear-gradient(92.74deg, $color-secondary 9.43%, #FF9844 96.54%);
  color: $color-white;
  // box-shadow: 0 4px 8px rgba(248, 133, 45, 0.3);
}

.confirm-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(248, 133, 45, 0.4);
}

.confirm-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(248, 133, 45, 0.3);
}

.full-width {
  width: 100%;
  flex: none;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
</style>