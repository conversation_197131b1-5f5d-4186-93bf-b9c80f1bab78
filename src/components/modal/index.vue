<template>
  <div v-if="visible" class="modal-wrapper" @click="handleModalClick">
    <div class="modal-container" :class="{ 'no-shadow': !hasShadow }">
      <slot />
      <!-- 图片区域 -->
      <img v-if="imgSrc" :src="imgSrc" alt="Modal image" class="modal-image">

      <!-- 标题 -->
      <h2 v-if="title" class="modal-title">
        {{ title }}
      </h2>

      <!-- 内容区域 -->
      <div v-if="content" class="modal-content">
        {{ content }}
      </div>

      <!-- 底部按钮区域 -->
      <div v-if="showButtons" class="modal-buttons">
        <!-- 取消按钮（当显示时） -->
        <button
          v-if="isShowCancel"
          class="modal-button cancel-button"
          @click="handleCancel"
        >
          {{ cancelText }}
        </button>

        <!-- 确认按钮（当显示时） -->
        <button
          v-if="isShowConfirm"
          class="modal-button confirm-button"
          :class="{ 'full-width': !isShowCancel }"
          @click="handleConfirm"
        >
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  // 是否展示底部按钮
  showButtons: {
    type: Boolean,
    default: true
  },
  // 展示图片
  imgSrc: {
    type: String,
    default: null
  },
  // 标题
  title: {
    type: String,
    default: null
  },
  // 内容
  content: {
    type: String,
    default: null
  },
  // 是否展示取消按钮
  isShowCancel: {
    type: Boolean,
    default: true
  },
  // 是否展示确认按钮
  isShowConfirm: {
    type: Boolean,
    default: true
  },
  // 取消文本
  cancelText: {
    type: String,
    default: "Quit"
  },
  // 确认文本
  confirmText: {
    type: String,
    default: "Continue"
  },
  // 点击遮罩层是否关闭弹窗
  closeOnClickModal: {
    type: Boolean,
    default: true
  },
  // 是否有阴影
  hasShadow: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:visible', 'cancel', 'confirm', 'markClickCancel']);

// 处理确认按钮点击
const handleConfirm = () => {
  emit('update:visible', false);
  emit('confirm');
};

// 处理取消按钮点击
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};

// 处理遮罩层点击
const handleModalClick = (e: MouseEvent) => {
  if (JSON.stringify(e.target) === JSON.stringify(e.currentTarget) && props.closeOnClickModal) {
    emit('update:visible', false);
    emit('markClickCancel');
  }
};
</script>

<style lang="scss" scoped>
/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.modal-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(22, 22, 22, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  animation: fadeIn 0.3s ease;
}

.modal-container {
  background-color: #fff;
  border-radius: 32rpx;
  width: min(90%, 400px);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.modal-container.no-shadow {
  box-shadow: none;
}

.modal-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
}

.modal-title {
  font-size:$font-size-heading-sm;
  font-weight: 700;
  text-align: center;
  font-family:var(--font-family-title);
}

.modal-content {
  font-size: $font-size-base;
  font-family:var(--font-family-regular);
  color:rgb(140, 139, 139);
  line-height: 1.5;
  text-align: center;
  margin-bottom: 8px;
}

.modal-buttons {
  display: flex;
  gap: 16px;
}

.modal-button {
  padding: 20rpx 48rpx;
  font-size: $font-size-md;
  font-weight: 600;
  border-radius: 140rpx;
  border: none;
  cursor: pointer;
  flex: 1;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: transparent;
  color: $color-secondary;
  border: 1px solid $color-secondary;
}

.cancel-button:hover {
  background-color:rgba(255, 241, 230, 1);
  // background: linear-gradient(92.74deg, $color-secondary 0.1%, #FF9844 0.1%);
}

.confirm-button {
  /* background: linear-gradient(135deg, #FFB472 0%, #F8852D 100%); */
  background: linear-gradient(92.74deg, $color-secondary 9.43%, #FF9844 96.54%);
  color: $color-white;
  // box-shadow: 0 4px 8px rgba(248, 133, 45, 0.3);
}

.confirm-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(248, 133, 45, 0.4);
}

.confirm-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(248, 133, 45, 0.3);
}

.full-width {
  width: 100%;
  flex: none;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
</style>
