import * as Pinia from 'pinia'
import { createSSRApp } from 'vue'
import { Locale } from 'wot-design-uni'

// 引入英文语言包
import enUS from 'wot-design-uni/locale/lang/en-US'
import { px2rpx, rpx2px } from './utils/rpxTopx'

// main.js
import * as api from '@/api'
import App from './App.vue'

export function createApp() {
  const app = createSSRApp(App)
  
  Locale.use('en-US', enUS)

  app.provide('$api', api)
  // 使用全局配置来挂载方法
  app.config.globalProperties.$px2rpx = px2rpx
  app.config.globalProperties.$rpx2px = rpx2px
  app.use(Pinia.createPinia())
  return {
    app,
    Pinia,
  }
}
