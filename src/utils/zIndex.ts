// src/constants/zIndex.ts
export const Z_INDEX_RULES = {
  // 页面元素层级 (1-999)
  PAGE: {
    MIN: 1,
    MAX: 999,
    TAB_BAR: 500,    // 固定为500
    CONTENT: 100,    // 页面内容
    FLOATING_BUTTON: 450, // 悬浮按钮
  },
  
  // 导航栏层级 (1000-1999)
  NAVIGATION: {
    MIN: 1000,
    MAX: 1999,
    HEADER: 1000,     // 页面头部导航
    SIDE_MENU: 1100,  // 侧边栏菜单
  },
  
  // 遮罩蒙层级 (2000及以上)
  MASK: {
    MIN: 2000,
    OVERLAY: 2000,    // 遮罩层
    MODAL: 2100,      // 模态框
    TOAST: 2200,      // 提示信息
    LOADING: 2300,    // 加载提示
  }
} as const;
