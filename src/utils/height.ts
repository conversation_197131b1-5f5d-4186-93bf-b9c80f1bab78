import { ref, computed } from 'vue'
import { rpx2px } from './rpxTopx'

export function useNavBar(initialNavBarHeight: number = 88) {
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync()
  
  // 状态栏高度，如果没有则使用默认值
  const statusBarHeight = ref<number>(systemInfo.statusBarHeight || 44)
  
  // 导航栏高度，转换为px
  const navBarHeight = ref<number>(rpx2px(initialNavBarHeight))
  
  // 计算总占位高度
  const placeholderHeight = computed<number>(() => 
    statusBarHeight.value + navBarHeight.value
  )
  
  // 获取当前页面信息
  const getCurrentPage = () => {
    const pages = getCurrentPages()
    return pages[pages.length - 1]
  }
  
  // 动态设置导航栏标题
  const setNavBarTitle = (title: string) => {
    uni.setNavigationBarTitle({
      title
    })
  }
  
  // 设置导航栏样式
  const setNavBarStyle = (options: {
    backgroundColor?: string
    frontColor?: string
    animation?: {
      duration?: number
      timingFunc?: string
    }
  }) => {
    uni.setNavigationBarColor({
      frontColor: options.frontColor || '#000000',
      backgroundColor: options.backgroundColor || '#ffffff',
      animation: options.animation || {
        duration: 0,
        timingFunc: 'easeIn'
      }
    })
  }
  
  // 隐藏/显示导航栏
  const toggleNavBar = (visible: boolean) => {
    uni.hideNavigationBarLoading()
    if (!visible) {
      uni.hideNavigationBarLoading()
    }
  }
  
  // 获取安全区域信息
  const safeArea = computed(() => ({
    top: statusBarHeight.value,
    bottom: systemInfo.safeAreaInsets?.bottom || 0,
    left: systemInfo.safeAreaInsets?.left || 0,
    right: systemInfo.safeAreaInsets?.right || 0
  }))
  
  // 判断是否为刘海屏
  const isNotchScreen = computed(() => {
    return statusBarHeight.value > 44
  })
  
  return {
    // 基础数据
    statusBarHeight,
    navBarHeight,
    placeholderHeight,
    safeArea,
    
    // 状态判断
    isNotchScreen,
    
    // 方法
    setNavBarTitle,
    setNavBarStyle,
    toggleNavBar,
    getCurrentPage
  }
}