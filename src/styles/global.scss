// 主色调 - 鲜艳橙色（用于重要按钮、图标等）
$color-primary: #F8852D;

// 次要色调 - 浅橙色（用于次要按钮、背景等）
$color-secondary: #FFB472;

// 浅色背景 - 淡橙色（用于卡片背景、标签背景等）
$color-background-light: #FFCAA1;

// 中性色调 - 浅米色（用于背景、分隔线等）
$color-neutral: #E9D5C3;

// 浅灰色背景 - 米色偏灰（用于页面背景、输入框背景等）
$color-background-gray: #F3E8DF;

// 强调色 - 深橙色（用于警告、提示等）
$color-accent: #F493A7;

// 强调色 - 浅橙色（用于警告、提示等）
$color-light: #F9CAD3;

// 白色 
$color-white: #FFFFFF;
// 黑色
$color-black: #262626;

//辅色体系 
// 暖色调 - 橙色系
$auxiliary-orange-light: #FFC9A0; // 浅橙色 - 鸢尾主调
$auxiliary-orange-main: #EA6500; // 橙色 - 鸢尾强调色

// 冷色调 - 绿色系
$auxiliary-green-light: #C1F5D9; // 浅绿 - 鸢尾主调
$auxiliary-green-main: #6EE39F; // 正绿 - 鸢尾主调

// 冷色调 - 蓝色系
$auxiliary-ice-light: #EDF2F7; // 浅冰蓝 - 鸢尾主调
$auxiliary-sky-light: #B1E5FC; // 浅天蓝 - 鸢尾表面色

// 辅色7 
$auxiliary-red-light: #FF6757; // 红 - 强调色

// 通用功能色
$auxiliary-border: #546FFF; // 边框专用色（重用橙色）


// 渐变色 (按钮)
$gradient-color-light: linear-gradient(92.74deg, #FFB173 9.43%, #FF9844 96.54%);

//首页朋友圈卡片背景色
$color-FF9B00: #FF9B00; // 橙色
// 渐变色3 (标题)
$gradient-color-title: linear-gradient(92.74deg, #FF9037 9.43%, #F86F00 96.54%);

/* 四象八字渐变色 */
$gradient-color-one-element: linear-gradient(113.59deg, #9A5315 7.58%, #D78E4B 100%);
$gradient-color-two-element: linear-gradient(113.59deg, #FE6C07 7.58%, #FF9929 100%);
$gradient-color-three-element: linear-gradient(113.59deg, #2557A6 7.58%, #44A6EB 100%);
$gradient-color-four-element: linear-gradient(113.59deg, #216063 7.58%, #349CAB 100%);