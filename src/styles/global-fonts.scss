@font-face {
  font-family: 'Product Sans';
  src: url('../static/font/Product-Sans.ttf') format('woff2');
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Product Sans Light';
  src: url('../static/font/Product-Sans-Light.ttf') format('woff2');
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Product Sans Regular';
  src: url('../static/font/ProductSans-Regular.ttf') format('woff2');
  font-style: normal;
  font-display: swap;
  /* 避免加载期布局偏移 */
}

:root {
  --font-family-title: 'Product Sans';
  --font-family-light: 'Product Sans Light';
  --font-family-medium: 'Product Sans Medium';
  --font-family-regular: 'Product Sans Regular';
  // --font-size-base: 32rpx;
  // --font-size-lg: 48rpx;
  // --font-size-md: 40rpx;
  // --font-size-sm: 32rpx;
  // --font-size-heading-lg: 30rpx;
  // --font-size-heading-md: 64rpx;
  // --font-size-heading-sm: 52rpx;
}

// ===== 1. 字号定义 =====
$font-size-base: 24rpx;
$font-size-lg: 32rpx;
$font-size-md: 28rpx;
$font-size-mdx: 26rpx;
$font-size-sm: 24rpx;
$font-size-xs: 20rpx;
$font-size-xxls: 18rpx;
$font-size-xxs: 16rpx;
$font-size-heading-lg: 60rpx;
$font-size-heading-xl: 64rpx;
$font-size-heading-md: 48rpx;
$font-size-heading-sm: 36rpx;
$font-size-heading-base: 40rpx;
$font-size-heading-mdx: 44rpx;
$font-size-heading-ml: 56rpx;

/* ===== 1. 字重定义 ===== */
$font-weight-bold: 700;
$font-weight-medium: 500;
$font-weight-regular: 400;
$font-weight-light: 300;

/* ===== 2. 层级封装 ===== */
/* Heading 1 */
.font-h1 {
  font-size: $font-size-heading-lg;
  // line-height: 38px;

  &-bold {
    font-weight: $font-weight-bold;
  }

  &-medium {
    font-weight: $font-weight-medium;
  }

  &-regular {
    font-weight: $font-weight-regular;
  }

  &-light {
    font-weight: $font-weight-light;
  }
}

/* Heading 2  */
.font-h2 {
  font-size: $font-size-heading-md;
  // line-height: 32px;

  &-bold {
    font-weight: $font-weight-bold;
  }

  &-medium {
    font-weight: $font-weight-medium;
  }

  &-regular {
    font-weight: $font-weight-regular;
  }
}

/* Heading 3 - 18px/26px */
.font-h3 {
  font-size: 18px;
  line-height: 26px;

  &-bold {
    font-weight: $font-weight-bold;
  }

  &-medium {
    font-weight: $font-weight-medium;
  }

  &-regular {
    font-weight: $font-weight-regular;
  }
}

/* Body Large - 16px/24px */
.font-body-lg {
  font-size: $font-size-heading-sm;
  // line-height: 24px;

  &-bold {
    font-weight: $font-weight-bold;
  }

  &-medium {
    font-weight: $font-weight-medium;
  }

  &-regular {
    font-weight: $font-weight-regular;
  }
}

/* Body Medium - 14px/20px */
.font-body-md {
  font-size: $font-size-md;
  // line-height: 20px;

  &-bold {
    font-weight: $font-weight-bold;
  }

  &-medium {
    font-weight: $font-weight-medium;
  }

  &-regular {
    font-weight: $font-weight-regular;
  }

  &-light {
    font-weight: $font-weight-light;
  }
}

/* Body Small - 12px/16px */
.font-body-sm {
  font-size: $font-size-sm;
  // line-height: 16px;

  &-bold {
    font-weight: $font-weight-bold;
  }

  &-medium {
    font-weight: $font-weight-medium;
  }

  &-regular {
    font-weight: $font-weight-regular;
  }

  &-light {
    font-weight: $font-weight-light;
  }
}

/* ===== 3. 特殊组合 ===== */
/* 标题+正文组合 (H1 + Body) */
.font-combo-heading-body {
  .heading {
    @extend .font-h1;
    margin-bottom: 24rpx;
  }

  .body {
    @extend .font-body-md;
    margin-top: 16rpx;
  }
}

/* 代码字体 */
.font-code {
  font-family: 'Courier New', monospace;
  font-family: 'Product Sans';
}

/* 响应式字体 */
/* @media (max-width: 768px) {
  .font-base {
    font-size: 14px;
  }
} */