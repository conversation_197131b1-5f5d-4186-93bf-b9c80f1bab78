// src/config/tabbar.ts
// 在这里定义你的 tabbar 列表
// uni-app 自定义 tabbar 文档: https://uniapp.dcloud.net.cn/collocation/pages.html#customtabbar
const tabConfig = [
  {
    tabIndex: 0,
    pagePath: 'subPackages/baby/index',
    text: 'Baby',
    icon: '/static/tabbar/baby.png', // 这里可以使用 wot-design-uni 的图标名或图片路径
    selectedIcon: '/static/tabbar/baby_active.png',
  },
  {
    tabIndex: 1,
    pagePath: 'subPackages/accompany/index',
    text: 'Accompany',
    icon: '/static/tabbar/accompany.png',
    selectedIcon: '/static/tabbar/accompany_active.png',
  },
  {
    pagePath: 'subPackages/publish/index',
    text: 'publish',
    isSpecial: true,
    // icon: 'https://xinliushijie.guest.geekdanceshop.com/upload/common/images/20250619/20250619052831175032531184078.jpg', // 这里可以使用 wot-design-uni 的图标名或图片路径
  },
  {
    tabIndex: 2,
    pagePath: 'subPackages/store/index',
    text: 'Store',
    icon: '/static/tabbar/store.png',
    selectedIcon: '/static/tabbar/store_active.png',
  },
  {
    tabIndex: 3,
    pagePath: 'subPackages/mine/index',
    text: 'Mine',
    icon: '/static/tabbar/mine.png',
    selectedIcon: '/static/tabbar/mine_active.png',
  },
]
export default tabConfig
// 如果你的 tabbar 有中间的凸起按钮，可以这样配置
export const midButton = {
  // midButton: true,
  // icon: 'plus',
  // width: '120rpx',
  // height: '120rpx'
}
